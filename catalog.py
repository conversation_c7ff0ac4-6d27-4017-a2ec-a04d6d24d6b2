import os
import json
from pathlib import Path

def create_feature_catalog():
    """
    Create a comprehensive catalog of all features in the app by mapping
    HTML pages to their corresponding CSS and JS files.
    """
    catalog = {
        "features": {},
        "metadata": {
            "total_features": 0,
            "html_files": 0,
            "css_files": 0,
            "js_files": 0,
            "orphaned_files": []
        }
    }
    
    # Get all HTML files (main features/pages)
    html_files = []
    for file in os.listdir("."):
        if file.endswith(".html"):
            html_files.append(file)
    
    # Get all CSS files
    css_files = []
    css_dir = "css"
    if os.path.exists(css_dir):
        for file in os.listdir(css_dir):
            if file.endswith(".css"):
                css_files.append(file)
    
    # Get all JS files
    js_files = []
    js_dir = "js"
    if os.path.exists(js_dir):
        for file in os.listdir(js_dir):
            if file.endswith(".js"):
                js_files.append(file)
    
    # Create feature mapping based on HTML files
    for html_file in html_files:
        feature_name = html_file.replace(".html", "")
        
        # Skip generic files
        if feature_name in ["index", "404"]:
            continue
            
        catalog["features"][feature_name] = {
            "html": html_file,
            "css": None,
            "js": None,
            "related_files": []
        }
        
        # Find matching CSS file
        potential_css = f"{feature_name}.css"
        if potential_css in css_files:
            catalog["features"][feature_name]["css"] = f"css/{potential_css}"
        
        # Find matching JS file
        potential_js = f"{feature_name}.js"
        if potential_js in js_files:
            catalog["features"][feature_name]["js"] = f"js/{potential_js}"
    
    # Add special cases and index page
    catalog["features"]["index"] = {
        "html": "index.html",
        "css": "styles/index.css" if os.path.exists("styles/index.css") else None,
        "js": None,
        "related_files": []
    }
    
    # Track orphaned files (CSS/JS without corresponding HTML)
    used_css = set()
    used_js = set()
    
    for feature in catalog["features"].values():
        if feature["css"]:
            used_css.add(feature["css"].split("/")[-1])
        if feature["js"]:
            used_js.add(feature["js"].split("/")[-1])
    
    # Find orphaned CSS files
    for css_file in css_files:
        if css_file not in used_css:
            catalog["metadata"]["orphaned_files"].append(f"css/{css_file}")
    
    # Find orphaned JS files
    for js_file in js_files:
        if js_file not in used_js:
            catalog["metadata"]["orphaned_files"].append(f"js/{js_file}")
    
    # Update metadata
    catalog["metadata"]["total_features"] = len(catalog["features"])
    catalog["metadata"]["html_files"] = len(html_files)
    catalog["metadata"]["css_files"] = len(css_files)
    catalog["metadata"]["js_files"] = len(js_files)
    
    return catalog

if __name__ == "__main__":
    catalog = create_feature_catalog()
    print(json.dumps(catalog, indent=2))
