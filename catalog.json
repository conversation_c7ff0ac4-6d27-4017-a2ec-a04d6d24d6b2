{"features": {"academic-details": {"html": "academic-details.html", "css": "css/academic-details.css", "js": "js/academic-details.js", "related_files": []}, "daily-calendar": {"html": "daily-calendar.html", "css": "css/daily-calendar.css", "js": null, "related_files": []}, "extracted": {"html": "extracted.html", "css": "css/extracted.css", "js": null, "related_files": []}, "flashcards": {"html": "flashcards.html", "css": "css/flashcards.css", "js": "js/flashcards.js", "related_files": []}, "grind": {"html": "grind.html", "css": null, "js": null, "related_files": []}, "instant-test-feedback": {"html": "instant-test-feedback.html", "css": null, "js": null, "related_files": []}, "landing": {"html": "landing.html", "css": null, "js": null, "related_files": []}, "priority-calculator": {"html": "priority-calculator.html", "css": "css/priority-calculator.css", "js": null, "related_files": []}, "priority-list": {"html": "priority-list.html", "css": "css/priority-list.css", "js": null, "related_files": []}, "settings": {"html": "settings.html", "css": "css/settings.css", "js": null, "related_files": []}, "sleep-saboteurs": {"html": "sleep-saboteurs.html", "css": "css/sleep-saboteurs.css", "js": null, "related_files": []}, "study-spaces": {"html": "study-spaces.html", "css": "css/study-spaces.css", "js": null, "related_files": []}, "subject-marks": {"html": "subject-marks.html", "css": "css/subject-marks.css", "js": "js/subject-marks.js", "related_files": []}, "tasks": {"html": "tasks.html", "css": null, "js": null, "related_files": []}, "workspace": {"html": "workspace.html", "css": "css/workspace.css", "js": null, "related_files": []}, "index": {"html": "index.html", "css": "styles/index.css", "js": null, "related_files": []}}, "metadata": {"total_features": 16, "html_files": 17, "css_files": 22, "js_files": 101, "orphaned_files": ["css/ai-search-response.css", "css/alarm-service.css", "css/compact-style.css", "css/notification.css", "css/sideDrawer.css", "css/simulation-enhancer.css", "css/task-display.css", "css/task-notes.css", "css/taskLinks.css", "css/test-feedback.css", "css/text-expansion.css", "js/add-favicon.js", "js/ai-latex-conversion.js", "js/ai-researcher.js", "js/alarm-data-service.js", "js/alarm-handler.js", "js/alarm-mini-display.js", "js/alarm-service-worker.js", "js/alarm-service.js", "js/api-optimization.js", "js/api-settings.js", "js/apiSettingsManager.js", "js/auth.js", "js/calendar-views.js", "js/calendarManager.js", "js/clock-display.js", "js/common-header.js", "js/common.js", "js/cross-tab-sync.js", "js/currentTaskManager.js", "js/data-loader.js", "js/data-sync-integration.js", "js/data-sync-manager.js", "js/energyHologram.js", "js/energyLevels.js", "js/fileViewer.js", "js/firebase-config.js", "js/firebase-init.js", "js/firebaseAuth.js", "js/firebaseConfig.js", "js/firestore-global.js", "js/firestore.js", "js/flashcardManager.js", "js/flashcardTaskIntegration.js", "js/gemini-api.js", "js/googleDriveApi.js", "js/googleGenerativeAI.js", "js/grind-speech-synthesis.js", "js/imageAnalyzer.js", "js/indexedDB.js", "js/initFirestoreData.js", "js/inject-header.js", "js/markdown-converter.js", "js/marks-tracking.js", "js/pandoc-fallback.js", "js/pomodoroGlobal.js", "js/pomodoroTimer.js", "js/priority-list-sorting.js", "js/priority-list-utils.js", "js/priority-sync-fix.js", "js/priority-worker-wrapper.js", "js/quoteManager.js", "js/recipeManager.js", "js/reorganize-scripts.js", "js/roleModelManager.js", "js/scheduleManager.js", "js/semester-management.js", "js/sideDrawer.js", "js/simulation-enhancer.js", "js/sleep-saboteurs-init.js", "js/sleepScheduleManager.js", "js/sleepTimeCalculator.js", "js/sm2.js", "js/soundManager.js", "js/speech-recognition.js", "js/speech-synthesis.js", "js/storageManager.js", "js/studySpaceAnalyzer.js", "js/studySpacesFirestore.js", "js/studySpacesManager.js", "js/subject-management.js", "js/subject-marks-integration.js", "js/subject-marks-ui.js", "js/task-notes-injector.js", "js/task-notes.js", "js/taskAttachments.js", "js/taskFilters.js", "js/taskLinks.js", "js/tasksManager.js", "js/test-feedback.js", "js/text-expansion.js", "js/theme-manager.js", "js/themeManager.js", "js/timetableAnalyzer.js", "js/timetableIntegration.js", "js/todoistIntegration.js", "js/transitionManager.js", "js/ui-utilities.js", "js/update-html-files.js", "js/userGuidance.js", "js/weightage-connector.js", "js/workspace-attachments.js", "js/workspace-core.js", "js/workspace-document.js", "js/workspace-formatting.js", "js/workspace-media.js", "js/workspace-tables-links.js", "js/workspace-ui.js", "js/workspaceFlashcardIntegration.js"]}}