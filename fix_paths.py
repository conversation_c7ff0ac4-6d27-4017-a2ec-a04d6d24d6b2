import re
import glob
import os
from pathlib import Path

def analyze_html_references():
    """
    Analyze HTML files to understand current reference patterns before fixing.
    """
    print("🔍 Analyzing HTML files for reference patterns...")
    print("=" * 60)
    
    patterns_found = {
        'css_links': set(),
        'js_scripts': set(),
        'other_refs': set()
    }
    
    html_files = glob.glob("features/*/*.html")
    
    for html_file in html_files:
        print(f"\n📄 Analyzing: {html_file}")
        
        with open(html_file, 'r', encoding='utf-8') as f:
            content = f.read()
            
            # Find CSS links
            css_matches = re.findall(r'href="([^"]*\.css[^"]*)"', content, re.IGNORECASE)
            for match in css_matches:
                patterns_found['css_links'].add(match)
                print(f"   🎨 CSS: {match}")
            
            # Find JS scripts
            js_matches = re.findall(r'src="([^"]*\.js[^"]*)"', content, re.IGNORECASE)
            for match in js_matches:
                patterns_found['js_scripts'].add(match)
                print(f"   ⚡ JS: {match}")
            
            # Find other references (images, etc.)
            other_matches = re.findall(r'(?:src|href)="([^"]*(?:\.png|\.jpg|\.jpeg|\.gif|\.svg|\.ico)[^"]*)"', content, re.IGNORECASE)
            for match in other_matches:
                patterns_found['other_refs'].add(match)
                print(f"   🖼️  Other: {match}")
    
    return patterns_found

def fix_html_paths():
    """
    Fix HTML file paths to use relative references within feature folders.
    """
    print("\n🛠️  Fixing HTML file paths...")
    print("=" * 60)
    
    html_files = glob.glob("features/*/*.html")
    fixed_files = []
    
    for html_file in html_files:
        print(f"\n📝 Processing: {html_file}")
        
        # Read the file
        with open(html_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        changes_made = []
        
        # Fix CSS links
        # Pattern: href="css/filename.css" -> href="./filename.css"
        css_pattern = r'href="css/([^"]+\.css)"'
        css_matches = re.findall(css_pattern, content)
        if css_matches:
            content = re.sub(css_pattern, r'href="./\1"', content)
            for match in css_matches:
                changes_made.append(f"CSS: css/{match} → ./{match}")
        
        # Fix CSS links with ../ patterns
        css_pattern2 = r'href="\.\./css/([^"]+\.css)"'
        css_matches2 = re.findall(css_pattern2, content)
        if css_matches2:
            content = re.sub(css_pattern2, r'href="./\1"', content)
            for match in css_matches2:
                changes_made.append(f"CSS: ../css/{match} → ./{match}")
        
        # Fix JS scripts
        # Pattern: src="js/filename.js" -> src="./filename.js"
        js_pattern = r'src="js/([^"]+\.js)"'
        js_matches = re.findall(js_pattern, content)
        if js_matches:
            content = re.sub(js_pattern, r'src="./\1"', content)
            for match in js_matches:
                changes_made.append(f"JS: js/{match} → ./{match}")
        
        # Fix JS scripts with ../ patterns
        js_pattern2 = r'src="\.\./js/([^"]+\.js)"'
        js_matches2 = re.findall(js_pattern2, content)
        if js_matches2:
            content = re.sub(js_pattern2, r'src="./\1"', content)
            for match in js_matches2:
                changes_made.append(f"JS: ../js/{match} → ./{match}")
        
        # Fix styles directory references
        styles_pattern = r'href="styles/([^"]+\.css)"'
        styles_matches = re.findall(styles_pattern, content)
        if styles_matches:
            content = re.sub(styles_pattern, r'href="../../styles/\1"', content)
            for match in styles_matches:
                changes_made.append(f"Styles: styles/{match} → ../../styles/{match}")
        
        # Fix root-level asset references
        # Pattern: src="filename.ext" -> src="../../filename.ext" (for root assets)
        root_assets = ['pop.mp3', 'worker.js', 'test-worker.js']
        for asset in root_assets:
            if f'src="{asset}"' in content:
                content = content.replace(f'src="{asset}"', f'src="../../{asset}"')
                changes_made.append(f"Root asset: {asset} → ../../{asset}")
        
        # Write back if changes were made
        if content != original_content:
            with open(html_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            fixed_files.append(html_file)
            print(f"   ✅ Fixed {len(changes_made)} references:")
            for change in changes_made:
                print(f"      {change}")
        else:
            print(f"   ℹ️  No changes needed")
    
    return fixed_files

def fix_js_imports():
    """
    Fix JavaScript import statements and require calls.
    """
    print("\n🔧 Fixing JavaScript imports and requires...")
    print("=" * 60)
    
    js_files = glob.glob("features/*/*.js")
    fixed_files = []
    
    for js_file in js_files:
        print(f"\n⚡ Processing: {js_file}")
        
        # Read the file
        with open(js_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        changes_made = []
        
        # Fix require statements
        # Pattern: require('../js/filename') -> require('./filename')
        require_pattern = r"require\(['\"]\.\.\/js\/([^'\"]+)['\"]\)"
        require_matches = re.findall(require_pattern, content)
        if require_matches:
            content = re.sub(require_pattern, r"require('./\1')", content)
            for match in require_matches:
                changes_made.append(f"Require: ../js/{match} → ./{match}")
        
        # Fix import statements
        # Pattern: import ... from '../js/filename' -> import ... from './filename'
        import_pattern = r"import\s+.*\s+from\s+['\"]\.\.\/js\/([^'\"]+)['\"]"
        import_matches = re.findall(import_pattern, content)
        if import_matches:
            content = re.sub(import_pattern, lambda m: m.group(0).replace('../js/', './'), content)
            for match in import_matches:
                changes_made.append(f"Import: ../js/{match} → ./{match}")
        
        # Fix ES6 dynamic imports
        # Pattern: import('../js/filename') -> import('./filename')
        dynamic_import_pattern = r"import\(['\"]\.\.\/js\/([^'\"]+)['\"]\)"
        dynamic_matches = re.findall(dynamic_import_pattern, content)
        if dynamic_matches:
            content = re.sub(dynamic_import_pattern, r"import('./\1')", content)
            for match in dynamic_matches:
                changes_made.append(f"Dynamic import: ../js/{match} → ./{match}")
        
        # Fix absolute js/ references
        js_abs_pattern = r"['\"]js\/([^'\"]+\.js)['\"]"
        js_abs_matches = re.findall(js_abs_pattern, content)
        if js_abs_matches:
            content = re.sub(js_abs_pattern, r"'../../js/\1'", content)
            for match in js_abs_matches:
                changes_made.append(f"Absolute JS: js/{match} → ../../js/{match}")
        
        # Write back if changes were made
        if content != original_content:
            with open(js_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            fixed_files.append(js_file)
            print(f"   ✅ Fixed {len(changes_made)} references:")
            for change in changes_made:
                print(f"      {change}")
        else:
            print(f"   ℹ️  No changes needed")
    
    return fixed_files

def fix_css_imports():
    """
    Fix CSS @import statements and url() references.
    """
    print("\n🎨 Fixing CSS imports and references...")
    print("=" * 60)
    
    css_files = glob.glob("features/*/*.css")
    fixed_files = []
    
    for css_file in css_files:
        print(f"\n🎨 Processing: {css_file}")
        
        # Read the file
        with open(css_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        changes_made = []
        
        # Fix @import statements
        import_pattern = r"@import\s+['\"]\.\.\/css\/([^'\"]+)['\"]"
        import_matches = re.findall(import_pattern, content)
        if import_matches:
            content = re.sub(import_pattern, r"@import './\1'", content)
            for match in import_matches:
                changes_made.append(f"@import: ../css/{match} → ./{match}")
        
        # Fix url() references
        url_pattern = r"url\(['\"]?\.\.\/([^'\"]+)['\"]?\)"
        url_matches = re.findall(url_pattern, content)
        if url_matches:
            content = re.sub(url_pattern, r"url('../../\1')", content)
            for match in url_matches:
                changes_made.append(f"url(): ../{match} → ../../{match}")
        
        # Write back if changes were made
        if content != original_content:
            with open(css_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            fixed_files.append(css_file)
            print(f"   ✅ Fixed {len(changes_made)} references:")
            for change in changes_made:
                print(f"      {change}")
        else:
            print(f"   ℹ️  No changes needed")
    
    return fixed_files

if __name__ == "__main__":
    print("🛠️  Agent 3: Relative-Path Rewiring")
    print("Goal: Batch-update <link> & <script> tags and import statements")
    print("=" * 60)
    
    # Step 1: Analyze current patterns
    patterns = analyze_html_references()
    
    # Step 2: Fix HTML paths
    html_fixed = fix_html_paths()
    
    # Step 3: Fix JavaScript imports
    js_fixed = fix_js_imports()
    
    # Step 4: Fix CSS imports
    css_fixed = fix_css_imports()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 PATH REWIRING SUMMARY")
    print("=" * 60)
    print(f"✅ HTML files fixed: {len(html_fixed)}")
    print(f"✅ JS files fixed: {len(js_fixed)}")
    print(f"✅ CSS files fixed: {len(css_fixed)}")
    
    if html_fixed:
        print(f"\n📄 HTML files updated:")
        for file in html_fixed:
            print(f"   {file}")
    
    if js_fixed:
        print(f"\n⚡ JavaScript files updated:")
        for file in js_fixed:
            print(f"   {file}")
    
    if css_fixed:
        print(f"\n🎨 CSS files updated:")
        for file in css_fixed:
            print(f"   {file}")
    
    print(f"\n🎉 Path rewiring complete! All references should now work correctly.")
