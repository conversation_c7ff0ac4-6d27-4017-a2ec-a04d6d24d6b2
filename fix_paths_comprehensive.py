import re
import glob
import os
import json
from pathlib import Path

def fix_remaining_paths():
    """
    Comprehensive path fixing for all remaining issues.
    """
    print("🔧 Comprehensive Path Fixing - Final Pass")
    print("=" * 60)
    
    html_files = glob.glob("features/*/*.html")
    total_fixes = 0
    
    for html_file in html_files:
        print(f"\n📝 Processing: {html_file}")
        
        with open(html_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        changes_made = []
        
        # Fix absolute paths that start with /js/
        absolute_js_pattern = r'src="/js/([^"]+)"'
        absolute_js_matches = re.findall(absolute_js_pattern, content)
        if absolute_js_matches:
            content = re.sub(absolute_js_pattern, r'src="../../js/\1"', content)
            for match in absolute_js_matches:
                changes_made.append(f"Absolute JS: /js/{match} → ../../js/{match}")
        
        # Fix absolute paths that start with /css/
        absolute_css_pattern = r'href="/css/([^"]+)"'
        absolute_css_matches = re.findall(absolute_css_pattern, content)
        if absolute_css_matches:
            content = re.sub(absolute_css_pattern, r'href="../../css/\1"', content)
            for match in absolute_css_matches:
                changes_made.append(f"Absolute CSS: /css/{match} → ../../css/{match}")
        
        # Fix asset references that need to go up two levels
        asset_patterns = [
            (r'src="assets/', r'src="../../assets/'),
            (r'href="assets/', r'href="../../assets/'),
            (r'src="icons/', r'src="../../icons/'),
            (r'href="icons/', r'href="../../icons/')
        ]
        
        for pattern, replacement in asset_patterns:
            if re.search(pattern, content):
                content = re.sub(pattern, replacement, content)
                changes_made.append(f"Asset path: {pattern} → {replacement}")
        
        # Fix navigation links to other features
        # Pattern: href="feature-name.html" → href="../feature-name/feature-name.html"
        nav_pattern = r'href="([a-zA-Z0-9-]+\.html)"'
        nav_matches = re.findall(nav_pattern, content)
        for match in nav_matches:
            if not match.startswith(('.', '/', 'http')):
                feature_name = match.replace('.html', '')
                old_href = f'href="{match}"'
                new_href = f'href="../{feature_name}/{match}"'
                if old_href in content:
                    content = content.replace(old_href, new_href)
                    changes_made.append(f"Navigation: {match} → ../{feature_name}/{match}")
        
        # Write back if changes were made
        if content != original_content:
            with open(html_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            total_fixes += len(changes_made)
            print(f"   ✅ Fixed {len(changes_made)} references:")
            for change in changes_made:
                print(f"      {change}")
        else:
            print(f"   ℹ️  No changes needed")
    
    return total_fixes

def fix_js_files_comprehensive():
    """
    Fix JavaScript files with comprehensive patterns.
    """
    print("\n🔧 Comprehensive JavaScript Fixing")
    print("=" * 60)
    
    js_files = glob.glob("features/*/*.js")
    total_fixes = 0
    
    for js_file in js_files:
        print(f"\n⚡ Processing: {js_file}")
        
        with open(js_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        changes_made = []
        
        # Fix any remaining absolute js/ references
        patterns_to_fix = [
            (r"'js/([^']+)'", r"'../../js/\1'"),
            (r'"js/([^"]+)"', r'"../../js/\1"'),
            (r"`js/([^`]+)`", r"`../../js/\1`"),
            (r"'/js/([^']+)'", r"'../../js/\1'"),
            (r'"/js/([^"]+)"', r'"../../js/\1"'),
            (r"`/js/([^`]+)`", r"`../../js/\1`")
        ]
        
        for pattern, replacement in patterns_to_fix:
            matches = re.findall(pattern, content)
            if matches:
                content = re.sub(pattern, replacement, content)
                for match in matches:
                    changes_made.append(f"JS reference: js/{match} → ../../js/{match}")
        
        # Write back if changes were made
        if content != original_content:
            with open(js_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            total_fixes += len(changes_made)
            print(f"   ✅ Fixed {len(changes_made)} references:")
            for change in changes_made:
                print(f"      {change}")
        else:
            print(f"   ℹ️  No changes needed")
    
    return total_fixes

def create_path_verification_report():
    """
    Create a report of all current path references to verify they're correct.
    """
    print("\n📊 Creating Path Verification Report")
    print("=" * 60)
    
    report = {
        "html_files": {},
        "js_files": {},
        "css_files": {},
        "issues_found": []
    }
    
    # Check HTML files
    html_files = glob.glob("features/*/*.html")
    for html_file in html_files:
        with open(html_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        css_refs = re.findall(r'href="([^"]*\.css[^"]*)"', content)
        js_refs = re.findall(r'src="([^"]*\.js[^"]*)"', content)
        asset_refs = re.findall(r'(?:src|href)="([^"]*(?:\.png|\.jpg|\.jpeg|\.gif|\.svg|\.ico)[^"]*)"', content)
        
        report["html_files"][html_file] = {
            "css_references": css_refs,
            "js_references": js_refs,
            "asset_references": asset_refs
        }
        
        # Check for potential issues
        for ref in css_refs + js_refs:
            if ref.startswith('/') and not ref.startswith('//') and not ref.startswith('http'):
                report["issues_found"].append(f"{html_file}: Absolute path {ref}")
            elif ref.startswith('css/') or ref.startswith('js/'):
                report["issues_found"].append(f"{html_file}: Old-style path {ref}")
    
    # Check JS files
    js_files = glob.glob("features/*/*.js")
    for js_file in js_files:
        with open(js_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        import_refs = re.findall(r'(?:import|require)\s*\([\'"`]([^\'"` ]+)[\'"`]\)', content)
        
        report["js_files"][js_file] = {
            "import_references": import_refs
        }
        
        # Check for potential issues
        for ref in import_refs:
            if ref.startswith('/js/') or ref.startswith('js/'):
                report["issues_found"].append(f"{js_file}: Old-style import {ref}")
    
    return report

if __name__ == "__main__":
    print("🛠️  Agent 3: Comprehensive Path Rewiring - Final Pass")
    print("=" * 60)
    
    # Fix remaining HTML paths
    html_fixes = fix_remaining_paths()
    
    # Fix remaining JS paths
    js_fixes = fix_js_files_comprehensive()
    
    # Create verification report
    report = create_path_verification_report()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 COMPREHENSIVE FIXING SUMMARY")
    print("=" * 60)
    print(f"✅ HTML fixes applied: {html_fixes}")
    print(f"✅ JS fixes applied: {js_fixes}")
    print(f"⚠️  Issues still found: {len(report['issues_found'])}")
    
    if report['issues_found']:
        print(f"\n⚠️  Remaining issues to review:")
        for issue in report['issues_found'][:10]:  # Show first 10
            print(f"   {issue}")
        if len(report['issues_found']) > 10:
            print(f"   ... and {len(report['issues_found']) - 10} more")
    
    # Save detailed report
    with open('path_verification_report.json', 'w') as f:
        json.dump(report, f, indent=2)
    
    print(f"\n📄 Detailed report saved as path_verification_report.json")
    print(f"🎉 Comprehensive path fixing complete!")
