import os
import json
from pathlib import Path

def analyze_feature_completeness():
    """
    Enhanced feature analysis that categorizes features by completeness
    and identifies potential relationships between files.
    """
    
    # Load the basic catalog
    with open('catalog.json', 'r') as f:
        catalog = json.load(f)
    
    enhanced_catalog = {
        "feature_analysis": {
            "complete_features": [],      # HTML + CSS + JS
            "partial_features": [],       # HTML + (CSS or JS)
            "minimal_features": [],       # HTML only
            "orphaned_components": []     # CSS/JS without HTML
        },
        "feature_categories": {
            "core_app": [],
            "academic_tools": [],
            "productivity": [],
            "ui_components": [],
            "data_management": [],
            "integrations": []
        },
        "statistics": {
            "total_features": 0,
            "complete_features_count": 0,
            "partial_features_count": 0,
            "minimal_features_count": 0,
            "orphaned_files_count": 0
        },
        "recommendations": []
    }
    
    # Analyze feature completeness
    for feature_name, feature_data in catalog["features"].items():
        has_html = feature_data["html"] is not None
        has_css = feature_data["css"] is not None
        has_js = feature_data["js"] is not None
        
        feature_info = {
            "name": feature_name,
            "files": feature_data,
            "completeness_score": sum([has_html, has_css, has_js])
        }
        
        if has_html and has_css and has_js:
            enhanced_catalog["feature_analysis"]["complete_features"].append(feature_info)
        elif has_html and (has_css or has_js):
            enhanced_catalog["feature_analysis"]["partial_features"].append(feature_info)
        elif has_html:
            enhanced_catalog["feature_analysis"]["minimal_features"].append(feature_info)
    
    # Categorize features by purpose
    academic_keywords = ["academic", "subject", "marks", "flashcards", "study"]
    productivity_keywords = ["task", "priority", "calendar", "workspace", "alarm"]
    ui_keywords = ["drawer", "notification", "display", "theme"]
    data_keywords = ["sync", "storage", "firebase", "api"]
    integration_keywords = ["todoist", "google", "gemini"]
    
    for feature_name in catalog["features"].keys():
        if feature_name in ["index", "landing", "settings"]:
            enhanced_catalog["feature_categories"]["core_app"].append(feature_name)
        elif any(keyword in feature_name.lower() for keyword in academic_keywords):
            enhanced_catalog["feature_categories"]["academic_tools"].append(feature_name)
        elif any(keyword in feature_name.lower() for keyword in productivity_keywords):
            enhanced_catalog["feature_categories"]["productivity"].append(feature_name)
        elif any(keyword in feature_name.lower() for keyword in ui_keywords):
            enhanced_catalog["feature_categories"]["ui_components"].append(feature_name)
        elif any(keyword in feature_name.lower() for keyword in data_keywords):
            enhanced_catalog["feature_categories"]["data_management"].append(feature_name)
        elif any(keyword in feature_name.lower() for keyword in integration_keywords):
            enhanced_catalog["feature_categories"]["integrations"].append(feature_name)
        else:
            enhanced_catalog["feature_categories"]["productivity"].append(feature_name)
    
    # Update statistics
    enhanced_catalog["statistics"]["total_features"] = len(catalog["features"])
    enhanced_catalog["statistics"]["complete_features_count"] = len(enhanced_catalog["feature_analysis"]["complete_features"])
    enhanced_catalog["statistics"]["partial_features_count"] = len(enhanced_catalog["feature_analysis"]["partial_features"])
    enhanced_catalog["statistics"]["minimal_features_count"] = len(enhanced_catalog["feature_analysis"]["minimal_features"])
    enhanced_catalog["statistics"]["orphaned_files_count"] = len(catalog["metadata"]["orphaned_files"])
    
    # Generate recommendations
    if enhanced_catalog["statistics"]["minimal_features_count"] > 0:
        enhanced_catalog["recommendations"].append(
            f"Consider adding CSS styling to {enhanced_catalog['statistics']['minimal_features_count']} minimal features"
        )
    
    if enhanced_catalog["statistics"]["orphaned_files_count"] > 50:
        enhanced_catalog["recommendations"].append(
            "High number of orphaned files detected - consider organizing or removing unused components"
        )
    
    # Add original catalog data
    enhanced_catalog["original_catalog"] = catalog
    
    return enhanced_catalog

if __name__ == "__main__":
    enhanced = analyze_feature_completeness()
    print(json.dumps(enhanced, indent=2))
