<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GPAce Workspace</title>
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="assets/images/gpace-logo-white.png">
    <link rel="shortcut icon" type="image/png" href="assets/images/gpace-logo-white.png">

    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap" rel="stylesheet">
    <link href="https://cdn.quilljs.com/1.3.6/quill.snow.css" rel="stylesheet">





<link rel="stylesheet" href="css/workspace.css">

    <style>
        /* Text-to-speech dropdown styles */
        #textToSpeechOptionsDropdown {
            display: none;
            position: absolute;
            background-color: var(--card-bg);
            min-width: 200px;
            box-shadow: 0px 8px 16px 0px rgba(0,0,0,0.2);
            z-index: 1000;
            border-radius: 4px;
            border: 1px solid var(--border-color);
            top: 100%;
            right: 0;
        }

        #textToSpeechOptionsDropdown.show {
            display: block;
        }

        /* Custom toast styles for text-to-speech */
        .tts-status-message {
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
    </style>

    <!-- Speech Modules -->




    <!-- Workspace Modules -->







</head>
<body>
    <div class="workspace-container">
        <div class="editor-container">
            <!-- Enhanced Toolbar Container -->
            <div class="toolbar-container">
                <!-- Primary Toolbar -->
                <div class="toolbar-row primary-tools">
                    <div class="toolbar-group">
                        <button class="toolbar-button" data-tooltip="New Document (Ctrl+N)">
                            <i class="bi bi-file-earmark-plus"></i>
                        </button>
                        <button class="toolbar-button" data-tooltip="Open (Ctrl+O)">
                            <i class="bi bi-folder2-open"></i>
                        </button>
                        <button class="toolbar-button" data-tooltip="Save (Ctrl+S)">
                            <i class="bi bi-save"></i>
                        </button>
                        <div class="toolbar-separator"></div>
                        <button class="toolbar-button" data-tooltip="Undo (Ctrl+Z)">
                            <i class="bi bi-arrow-counterclockwise"></i>
                        </button>
                        <button class="toolbar-button" data-tooltip="Redo (Ctrl+Y)">
                            <i class="bi bi-arrow-clockwise"></i>
                        </button>
                    </div>

                    <div class="toolbar-group">
                        <button class="toolbar-button" data-tooltip="Export as PDF">
                            <i class="bi bi-file-pdf"></i>
                        </button>
                        <button class="toolbar-button" data-tooltip="Export as Word">
                            <i class="bi bi-file-word"></i>
                        </button>
                    </div>

                    <div class="toolbar-group">
                        <button id="speechRecognitionBtn" class="toolbar-button" data-tooltip="Start Live Lecture Recording">
                            <i class="bi bi-mic"></i>
                        </button>
                        <button id="pauseResumeBtn" class="toolbar-button" data-tooltip="Pause/Resume Recording" disabled>
                            <i class="bi bi-pause-fill"></i>
                        </button>
                        <button id="summarizeBtn" class="toolbar-button" data-tooltip="Summarize Lecture" disabled>
                            <i class="bi bi-lightning-fill"></i>
                        </button>
                        <button id="speechLangSettingsBtn" class="toolbar-button" data-tooltip="Language Settings (Ctrl+L to toggle during recording)">
                            <i class="bi bi-translate"></i>
                        </button>
                        <span id="recordingTimer" class="recording-timer" style="display: none;">00:00:00</span>
                    </div>

                    <div class="toolbar-group">
                        <div class="editor-dropdown">
                            <button id="textToSpeechBtn" class="toolbar-button" data-tooltip="Text to Speech">
                                <i class="bi bi-volume-up"></i>
                            </button>
                            <div class="image-options-dropdown" id="textToSpeechOptionsDropdown">
                                <div class="dropdown-item" id="speakSelectedTextBtn">
                                    <i class="bi bi-cursor-text"></i>
                                    Read Selected Text
                                </div>
                                <div class="dropdown-item" id="speakAllTextBtn">
                                    <i class="bi bi-file-text"></i>
                                    Read Entire Document
                                </div>
                                <div class="dropdown-item" id="showTextToSpeechSettingsBtn">
                                    <i class="bi bi-gear"></i>
                                    Voice Settings
                                </div>
                            </div>
                        </div>
                        <button id="pauseResumeTextToSpeechBtn" class="toolbar-button" data-tooltip="Pause/Resume Reading" disabled>
                            <i class="bi bi-pause-fill"></i>
                        </button>
                        <button id="stopTextToSpeechBtn" class="toolbar-button" data-tooltip="Stop Reading" disabled>
                            <i class="bi bi-stop-fill"></i>
                        </button>
                    </div>
                </div>

                <!-- Formatting Toolbar -->
                <div class="toolbar-row formatting-tools">
                    <div class="toolbar-group">
                        <select class="toolbar-select font-family" id="fontFamily" data-tooltip="Font Family">
                            <option value="Inter">Inter</option>
                            <option value="Arial">Arial</option>
                            <option value="Helvetica">Helvetica</option>
                            <option value="Times New Roman">Times New Roman</option>
                            <option value="Georgia">Georgia</option>
                            <option value="Courier New">Courier New</option>
                        </select>
                        <select class="toolbar-select font-size" id="fontSize" data-tooltip="Font Size">
                            <option value="8">8</option>
                            <option value="9">9</option>
                            <option value="10">10</option>
                            <option value="11">11</option>
                            <option value="12" selected>12</option>
                            <option value="14">14</option>
                            <option value="16">16</option>
                            <option value="18">18</option>
                            <option value="24">24</option>
                            <option value="30">30</option>
                            <option value="36">36</option>
                        </select>
                    </div>

                    <div class="toolbar-group">
                        <button class="toolbar-button" data-format="bold" data-tooltip="Bold (Ctrl+B)">
                            <i class="bi bi-type-bold"></i>
                        </button>
                        <button class="toolbar-button" data-format="italic" data-tooltip="Italic (Ctrl+I)">
                            <i class="bi bi-type-italic"></i>
                        </button>
                        <button class="toolbar-button" data-format="underline" data-tooltip="Underline (Ctrl+U)">
                            <i class="bi bi-type-underline"></i>
                        </button>
                        <button class="toolbar-button" data-format="strike" data-tooltip="Strikethrough">
                            <i class="bi bi-type-strikethrough"></i>
                        </button>
                    </div>

                    <div class="toolbar-group">
                        <input type="color" class="toolbar-color-picker" id="textColor" data-tooltip="Text Color">
                        <input type="color" class="toolbar-color-picker" id="backgroundColor" data-tooltip="Background Color">
                    </div>

                    <div class="toolbar-group">
                        <button class="toolbar-button" data-format="alignLeft" data-tooltip="Align Left">
                            <i class="bi bi-text-left"></i>
                        </button>
                        <button class="toolbar-button" data-format="alignCenter" data-tooltip="Align Center">
                            <i class="bi bi-text-center"></i>
                        </button>
                        <button class="toolbar-button" data-format="alignRight" data-tooltip="Align Right">
                            <i class="bi bi-text-right"></i>
                        </button>
                        <button class="toolbar-button" data-format="justify" data-tooltip="Justify">
                            <i class="bi bi-justify"></i>
                        </button>
                    </div>

                    <div class="toolbar-group">
                        <div class="editor-dropdown">
                            <button class="toolbar-button" data-tooltip="Insert Image">
                                <i class="bi bi-image"></i>
                            </button>
                            <div class="image-options-dropdown" id="imageOptionsDropdown">
                                <div class="dropdown-item" id="uploadImageBtn">
                                    <i class="bi bi-upload"></i>
                                    Upload from Computer
                                </div>
                                <div class="dropdown-item" id="openGooglePickerBtn">
                                    <i class="bi bi-google"></i>
                                    Choose from Google Drive
                                </div>
                                <div class="dropdown-item" id="insertImageUrlBtn">
                                    <i class="bi bi-link-45deg"></i>
                                    Insert Image URL
                                </div>
                            </div>
                        </div>
                        <button class="toolbar-button" data-tooltip="Insert Link">
                            <i class="bi bi-link-45deg"></i>
                        </button>
                        <button class="toolbar-button" data-tooltip="Insert Table">
                            <i class="bi bi-table"></i>
                        </button>
                        <input type="file" id="imageUpload" accept="image/*" style="display: none">
                    </div>
                </div>
            </div>

            <!-- Enhanced Editor Content Area -->
            <div class="editor-content" id="editorContainer">

                <!-- Floating Formatting Toolbar -->
                <div class="floating-toolbar" id="floatingToolbar">
                    <button class="tool-btn" data-format="bold" data-tooltip="Bold">
                        <i class="bi bi-type-bold"></i>
                    </button>
                    <button class="tool-btn" data-format="italic" data-tooltip="Italic">
                        <i class="bi bi-type-italic"></i>
                    </button>
                    <button class="tool-btn" data-format="underline" data-tooltip="Underline">
                        <i class="bi bi-type-underline"></i>
                    </button>
                    <button class="tool-btn" data-format="strike" data-tooltip="Strikethrough">
                        <i class="bi bi-type-strikethrough"></i>
                    </button>
                    <button class="tool-btn" id="floatingInsertLinkBtn" data-tooltip="Insert Link">
                        <i class="bi bi-link-45deg"></i>
                    </button>
                </div>
                <div id="editor"></div>
                <div id="dropZone" class="drop-zone">
                    <i class="bi bi-cloud-upload"></i>
                    <span>Drop files here</span>
                </div>
            </div>

            <!-- We're removing the separate transcription container and using the existing Quill editor instead -->

            <!-- Enhanced Status Bar -->
            <div class="editor-statusbar">
                <div class="statusbar-left">
                    <div class="status-item">
                        <i class="bi bi-pencil"></i>
                        <span id="editorWordCount">0 words</span>
                    </div>
                    <div class="status-item">
                        <i class="bi bi-text-paragraph"></i>
                        <span id="editorCharCount">0 characters</span>
                    </div>
                </div>
                <div class="statusbar-right">
                    <div class="status-item zoom-controls">
                        <button class="toolbar-button" data-tooltip="Zoom Out">
                            <i class="bi bi-zoom-out"></i>
                        </button>
                        <span id="zoomLevel">100%</span>
                        <button class="toolbar-button" data-tooltip="Zoom In">
                            <i class="bi bi-zoom-in"></i>
                        </button>
                    </div>
                    <div class="status-item">
                        <i class="bi bi-clock"></i>
                        <span id="editorLastSaved">Never saved</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Status Message Container -->
        <div id="statusMessage" class="status-message" style="display: none;"></div>

        <!-- Task Attachments Section -->
        <div class="attachments-section" id="attachmentsSection">
            <div class="attachments-header">
                <h3>Task Attachments</h3>
                <select id="taskSelector" class="task-select">
                    <option value="">Select a task to view attachments</option>
                </select>
            </div>
            <div class="attachments-container" id="attachmentsContainer">
                <div class="no-attachments-message">
                    <i class="bi bi-file-earmark"></i>
                    <p>Select a task to view its attachments</p>
                </div>
            </div>
        </div>

        <!-- Task Status Bar -->
        <div class="status-bar">
            <div class="word-count">
                <span id="wordCount">0 words</span>
                <span id="charCount">0 characters</span>
            </div>
            <div id="lastSaved">Last saved: Never</div>
        </div>


    </div>

    <!-- Add theme toggle button -->
    <button class="theme-toggle">
        <span class="theme-icon">🌞</span>
        <span class="theme-text">Light Mode</span>
    </button>

    <!-- Image URL Dialog -->
    <div class="modal" id="imageUrlModal">
        <div class="modal-content">
            <h3>Insert Image URL</h3>
            <input type="text" id="imageUrl" placeholder="Enter image URL">
            <div class="modal-buttons">
                <button id="closeImageUrlBtn">Cancel</button>
                <button id="insertImageFromUrlBtn">Insert</button>
            </div>
        </div>
    </div>

    <!-- Link URL Dialog -->
    <div class="modal" id="linkUrlModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
        <div class="modal-content" style="position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); background: var(--card-bg); padding: 20px; border-radius: 8px; min-width: 300px;">
            <h3 style="margin-top: 0;">Insert Link</h3>
            <input type="text" id="linkUrl" placeholder="Enter URL" style="width: 100%; padding: 8px; margin: 10px 0; background: var(--background-color); color: var(--text-color); border: 1px solid var(--border-color); border-radius: 4px;">
            <input type="text" id="linkText" placeholder="Enter link text" style="width: 100%; padding: 8px; margin: 10px 0; background: var(--background-color); color: var(--text-color); border: 1px solid var(--border-color); border-radius: 4px;">
            <div class="modal-buttons" style="display: flex; justify-content: flex-end; gap: 10px; margin-top: 15px;">
                <button id="closeLinkDialogBtn" style="padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color); background: var(--background-color); color: var(--text-color);">Cancel</button>
                <button id="insertLinkFromDialogBtn" style="padding: 6px 12px; border-radius: 4px; border: none; background: var(--primary-color); color: white;">Insert</button>
            </div>
        </div>
    </div>

    <!-- Table Dialog -->
    <div class="modal" id="tableModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
        <div class="modal-content" style="position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); background: var(--card-bg); padding: 20px; border-radius: 8px; min-width: 300px;">
            <h3 style="margin-top: 0;">Insert Table</h3>
            <div style="margin: 10px 0;">
                <label style="display: block; margin-bottom: 5px;">Rows:</label>
                <input type="number" id="tableRows" value="2" min="1" max="10" style="width: 100%; padding: 8px; background: var(--background-color); color: var(--text-color); border: 1px solid var(--border-color); border-radius: 4px;">
            </div>
            <div style="margin: 10px 0;">
                <label style="display: block; margin-bottom: 5px;">Columns:</label>
                <input type="number" id="tableColumns" value="2" min="1" max="10" style="width: 100%; padding: 8px; background: var(--background-color); color: var(--text-color); border: 1px solid var(--border-color); border-radius: 4px;">
            </div>
            <div class="modal-buttons" style="display: flex; justify-content: flex-end; gap: 10px; margin-top: 15px;">
                <button id="closeTableDialogBtn" style="padding: 6px 12px; border-radius: 4px; border: 1px solid var(--border-color); background: var(--background-color); color: var(--text-color);">Cancel</button>
                <button id="insertTableFromDialogBtn" style="padding: 6px 12px; border-radius: 4px; border: none; background: var(--primary-color); color: white;">Insert</button>
            </div>
        </div>
    </div>














    <!-- Scripts -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/docx/7.8.2/docx.js"></script>
    <script src="https://cdn.quilljs.com/1.3.6/quill.min.js"></script>
    <script src="https://apis.google.com/js/api.js"></script>
    <script src="https://accounts.google.com/gsi/client"></script>
    <script src="js/speech-recognition.js"></script>
    <script src="js/speech-synthesis.js"></script>
    <script type="module" src="js/workspaceFlashcardIntegration.js"></script>
    <script type="module" src="js/cross-tab-sync.js"></script>
    <script src="js/workspace-ui.js"></script>
    <script src="js/workspace-formatting.js"></script>
    <script src="js/workspace-document.js"></script>
    <script src="js/workspace-media.js"></script>
    <script src="js/workspace-tables-links.js"></script>
    <script src="js/workspace-attachments.js"></script>
    <script src="js/workspace-core.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js"></script>
    <script src="js/sm2.js"></script>
    <script src="/js/inject-header.js"></script>
</body>
</html>