# 🚀 Agent 2: Bulk Folder Creation & Moves
# Goal: In one go, scaffold /features/*, move files, preserve structure

Write-Host "🚀 Agent 2: Bulk Folder Creation & Moves" -ForegroundColor Green
Write-Host "Goal: Scaffold /features/*, move files, preserve structure" -ForegroundColor Yellow
Write-Host "=" * 60

# Load catalog.json
try {
    $catalog = Get-Content catalog.json | ConvertFrom-Json
    Write-Host "✅ Loaded catalog.json successfully" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to load catalog.json: $_" -ForegroundColor Red
    exit 1
}

# Create features directory
if (!(Test-Path "features")) {
    New-Item -ItemType Directory -Name "features" -Force | Out-Null
    Write-Host "📁 Created features directory" -ForegroundColor Green
}

$movedFiles = @()
$errors = @()

Write-Host "`n🔀 Starting bulk folder creation and file moves..." -ForegroundColor Cyan
Write-Host "=" * 60

# Process each feature
foreach ($featureName in $catalog.features.PSObject.Properties.Name) {
    $feature = $catalog.features.$featureName
    
    Write-Host "`n📁 Processing feature: $featureName" -ForegroundColor White
    
    # Create feature directory
    $featureDir = "features\$featureName"
    if (!(Test-Path $featureDir)) {
        New-Item -ItemType Directory -Path $featureDir -Force | Out-Null
        Write-Host "   ✅ Created directory: $featureDir" -ForegroundColor Green
    }
    
    # Move HTML file
    if ($feature.html -and $feature.html -ne $null) {
        $htmlSrc = $feature.html
        $htmlDst = "$featureDir\$(Split-Path $htmlSrc -Leaf)"
        
        if (Test-Path $htmlSrc) {
            try {
                Move-Item $htmlSrc $htmlDst -Force
                $movedFiles += "$htmlSrc → $htmlDst"
                Write-Host "   📄 Moved HTML: $(Split-Path $htmlSrc -Leaf)" -ForegroundColor Blue
            } catch {
                $errors += "Failed to move $htmlSrc : $_"
                Write-Host "   ❌ Error moving $htmlSrc : $_" -ForegroundColor Red
            }
        } else {
            Write-Host "   ⚠️  HTML file not found: $htmlSrc" -ForegroundColor Yellow
        }
    }
    
    # Move CSS file
    if ($feature.css -and $feature.css -ne $null) {
        $cssSrc = $feature.css
        $cssDst = "$featureDir\$(Split-Path $cssSrc -Leaf)"
        
        if (Test-Path $cssSrc) {
            try {
                Move-Item $cssSrc $cssDst -Force
                $movedFiles += "$cssSrc → $cssDst"
                Write-Host "   🎨 Moved CSS: $(Split-Path $cssSrc -Leaf)" -ForegroundColor Magenta
            } catch {
                $errors += "Failed to move $cssSrc : $_"
                Write-Host "   ❌ Error moving $cssSrc : $_" -ForegroundColor Red
            }
        } else {
            Write-Host "   ⚠️  CSS file not found: $cssSrc" -ForegroundColor Yellow
        }
    }
    
    # Move JS file
    if ($feature.js -and $feature.js -ne $null) {
        $jsSrc = $feature.js
        $jsDst = "$featureDir\$(Split-Path $jsSrc -Leaf)"
        
        if (Test-Path $jsSrc) {
            try {
                Move-Item $jsSrc $jsDst -Force
                $movedFiles += "$jsSrc → $jsDst"
                Write-Host "   ⚡ Moved JS: $(Split-Path $jsSrc -Leaf)" -ForegroundColor Yellow
            } catch {
                $errors += "Failed to move $jsSrc : $_"
                Write-Host "   ❌ Error moving $jsSrc : $_" -ForegroundColor Red
            }
        } else {
            Write-Host "   ⚠️  JS file not found: $jsSrc" -ForegroundColor Yellow
        }
    }
}

# Move workers and related scripts
Write-Host "`n🔧 Processing worker files and related scripts..." -ForegroundColor Cyan
Write-Host "=" * 60

$movedWorkers = @()

# Move worker files if workers directory exists
if (Test-Path "workers") {
    $workerFiles = Get-ChildItem "workers\*.js"
    Write-Host "Found $($workerFiles.Count) worker files" -ForegroundColor White
    
    foreach ($workerFile in $workerFiles) {
        $workerName = $workerFile.BaseName
        $matched = $false
        
        # Look for matching feature
        $featureDirs = Get-ChildItem "features" -Directory
        foreach ($featureDir in $featureDirs) {
            $featureName = $featureDir.Name
            
            # Check if worker name matches feature name
            if ($featureName -like "*$workerName*" -or $workerName -like "*$featureName*" -or 
                $workerName.Replace('-','') -like "*$($featureName.Replace('-',''))*") {
                
                try {
                    $dst = "$($featureDir.FullName)\$($workerFile.Name)"
                    Move-Item $workerFile.FullName $dst -Force
                    $movedWorkers += "$($workerFile.FullName) → $dst"
                    Write-Host "   🔧 Moved worker: $($workerFile.Name) → features\$featureName\" -ForegroundColor Cyan
                    $matched = $true
                    break
                } catch {
                    $errors += "Failed to move $($workerFile.FullName): $_"
                    Write-Host "   ❌ Error moving $($workerFile.Name): $_" -ForegroundColor Red
                }
            }
        }
        
        if (-not $matched) {
            Write-Host "   ⚠️  No matching feature found for worker: $($workerFile.Name)" -ForegroundColor Yellow
        }
    }
}

# Move standalone files that might belong to features
$standaloneFiles = @(
    "priority-calculator.js",
    "priority-calculator-with-worker.js",
    "worker.js",
    "test-worker.js"
)

foreach ($fileName in $standaloneFiles) {
    if (Test-Path $fileName) {
        $baseName = $fileName -replace '\.js$','' -replace '-with-worker','' -replace 'test-',''
        
        $featureDirs = Get-ChildItem "features" -Directory
        foreach ($featureDir in $featureDirs) {
            if ($featureDir.Name -like "*$baseName*") {
                try {
                    $dst = "$($featureDir.FullName)\$fileName"
                    Move-Item $fileName $dst -Force
                    $movedWorkers += "$fileName → $dst"
                    Write-Host "   📦 Moved standalone: $fileName → features\$($featureDir.Name)\" -ForegroundColor Green
                    break
                } catch {
                    $errors += "Failed to move $fileName : $_"
                    Write-Host "   ❌ Error moving $fileName : $_" -ForegroundColor Red
                }
            }
        }
    }
}

# Summary
Write-Host "`n" + "=" * 60 -ForegroundColor White
Write-Host "📊 REORGANIZATION SUMMARY" -ForegroundColor Green
Write-Host "=" * 60 -ForegroundColor White
Write-Host "✅ Files moved: $($movedFiles.Count + $movedWorkers.Count)" -ForegroundColor Green
Write-Host "❌ Errors: $($errors.Count)" -ForegroundColor Red

if ($movedFiles.Count -gt 0) {
    Write-Host "`n📄 Main files moved ($($movedFiles.Count)):" -ForegroundColor Blue
    $movedFiles | Select-Object -First 10 | ForEach-Object { Write-Host "   $_" -ForegroundColor White }
    if ($movedFiles.Count -gt 10) {
        Write-Host "   ... and $($movedFiles.Count - 10) more" -ForegroundColor Gray
    }
}

if ($movedWorkers.Count -gt 0) {
    Write-Host "`n🔧 Workers/related moved ($($movedWorkers.Count)):" -ForegroundColor Cyan
    $movedWorkers | ForEach-Object { Write-Host "   $_" -ForegroundColor White }
}

if ($errors.Count -gt 0) {
    Write-Host "`n❌ Errors encountered:" -ForegroundColor Red
    $errors | ForEach-Object { Write-Host "   $_" -ForegroundColor Red }
}

Write-Host "`n🎉 Reorganization complete! Check the features/ directory." -ForegroundColor Green
