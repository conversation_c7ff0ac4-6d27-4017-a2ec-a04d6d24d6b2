import json
from pathlib import Path

def update_catalog_with_new_paths():
    """
    Update the catalog.json to reflect the new file locations after reorganization.
    """
    
    # Load the original catalog
    with open('catalog.json', 'r') as f:
        catalog = json.load(f)
    
    # Create updated catalog
    updated_catalog = {
        "features": {},
        "metadata": catalog["metadata"].copy(),
        "reorganization_info": {
            "structure": "feature-based",
            "base_path": "features/",
            "reorganized": True,
            "date": "2024"
        }
    }
    
    # Update file paths for each feature
    for feature_name, feature_data in catalog["features"].items():
        updated_feature = {
            "html": None,
            "css": None, 
            "js": None,
            "related_files": []
        }
        
        feature_dir = f"features/{feature_name}"
        
        # Update HTML path
        if feature_data["html"]:
            html_filename = Path(feature_data["html"]).name
            updated_feature["html"] = f"{feature_dir}/{html_filename}"
        
        # Update CSS path
        if feature_data["css"]:
            css_filename = Path(feature_data["css"]).name
            updated_feature["css"] = f"{feature_dir}/{css_filename}"
        
        # Update JS path
        if feature_data["js"]:
            js_filename = Path(feature_data["js"]).name
            updated_feature["js"] = f"{feature_dir}/{js_filename}"
        
        # Check for additional files that were moved
        feature_path = Path(feature_dir)
        if feature_path.exists():
            additional_files = []
            for file in feature_path.iterdir():
                if file.is_file():
                    file_rel_path = f"{feature_dir}/{file.name}"
                    # Add files that aren't already listed
                    if (file_rel_path != updated_feature["html"] and 
                        file_rel_path != updated_feature["css"] and 
                        file_rel_path != updated_feature["js"]):
                        additional_files.append(file_rel_path)
            
            updated_feature["related_files"] = additional_files
        
        updated_catalog["features"][feature_name] = updated_feature
    
    # Update metadata
    updated_catalog["metadata"]["structure_type"] = "feature-based"
    updated_catalog["metadata"]["reorganized"] = True
    
    return updated_catalog

def generate_feature_index():
    """
    Generate an index of all features with their completeness status.
    """
    
    updated_catalog = update_catalog_with_new_paths()
    
    feature_index = {
        "feature_summary": {
            "total_features": len(updated_catalog["features"]),
            "complete_features": [],
            "partial_features": [],
            "minimal_features": []
        },
        "features": {}
    }
    
    for feature_name, feature_data in updated_catalog["features"].items():
        has_html = feature_data["html"] is not None
        has_css = feature_data["css"] is not None  
        has_js = feature_data["js"] is not None
        
        completeness_score = sum([has_html, has_css, has_js])
        
        feature_info = {
            "path": f"features/{feature_name}",
            "files": feature_data,
            "completeness": {
                "score": completeness_score,
                "has_html": has_html,
                "has_css": has_css,
                "has_js": has_js,
                "status": "complete" if completeness_score == 3 else 
                         "partial" if completeness_score == 2 else "minimal"
            }
        }
        
        # Categorize by completeness
        if completeness_score == 3:
            feature_index["feature_summary"]["complete_features"].append(feature_name)
        elif completeness_score == 2:
            feature_index["feature_summary"]["partial_features"].append(feature_name)
        else:
            feature_index["feature_summary"]["minimal_features"].append(feature_name)
        
        feature_index["features"][feature_name] = feature_info
    
    return feature_index

if __name__ == "__main__":
    print("🔄 Updating catalog with new file paths...")
    
    # Update catalog
    updated_catalog = update_catalog_with_new_paths()
    
    # Save updated catalog
    with open('catalog_updated.json', 'w') as f:
        json.dump(updated_catalog, f, indent=2)
    
    print("✅ Updated catalog saved as catalog_updated.json")
    
    # Generate feature index
    feature_index = generate_feature_index()
    
    # Save feature index
    with open('feature_index.json', 'w') as f:
        json.dump(feature_index, f, indent=2)
    
    print("✅ Feature index saved as feature_index.json")
    
    # Print summary
    print(f"\n📊 Summary:")
    print(f"   Complete features: {len(feature_index['feature_summary']['complete_features'])}")
    print(f"   Partial features: {len(feature_index['feature_summary']['partial_features'])}")
    print(f"   Minimal features: {len(feature_index['feature_summary']['minimal_features'])}")
    print(f"   Total features: {feature_index['feature_summary']['total_features']}")
