{"navigation_links": {"features\\academic-details\\academic-details.html": ["../grind/grind.html", "../grind/grind.html", "../study-spaces/study-spaces.html", "../daily-calendar/daily-calendar.html", "../academic-details/academic-details.html", "../extracted/extracted.html", "../subject-marks/subject-marks.html", "../flashcards/flashcards.html", "../markdown-converter/markdown-converter.html"], "features\\daily-calendar\\daily-calendar.html": ["../grind/grind.html", "../grind/grind.html", "../study-spaces/study-spaces.html", "../daily-calendar/daily-calendar.html", "../academic-details/academic-details.html", "../extracted/extracted.html", "../subject-marks/subject-marks.html", "../flashcards/flashcards.html"], "features\\extracted\\extracted.html": ["../grind/grind.html", "../grind/grind.html", "../study-spaces/study-spaces.html", "../daily-calendar/daily-calendar.html", "../academic-details/academic-details.html", "../extracted/extracted.html", "../subject-marks/subject-marks.html", "../flashcards/flashcards.html"], "features\\flashcards\\flashcards.html": ["../grind/grind.html", "../grind/grind.html", "../study-spaces/study-spaces.html", "../daily-calendar/daily-calendar.html", "../academic-details/academic-details.html", "../extracted/extracted.html", "../subject-marks/subject-marks.html", "../flashcards/flashcards.html"], "features\\grind\\grind.html": ["../grind/grind.html", "../grind/grind.html", "../instant-test-feedback/instant-test-feedback.html", "../study-spaces/study-spaces.html", "../daily-calendar/daily-calendar.html", "../academic-details/academic-details.html", "../extracted/extracted.html", "../subject-marks/subject-marks.html", "../flashcards/flashcards.html"], "features\\index\\index.html": ["../grind/grind.html"], "features\\instant-test-feedback\\instant-test-feedback.html": ["../grind/grind.html", "../grind/grind.html", "../instant-test-feedback/instant-test-feedback.html", "../study-spaces/study-spaces.html", "../daily-calendar/daily-calendar.html", "../academic-details/academic-details.html", "../extracted/extracted.html", "../subject-marks/subject-marks.html", "../flashcards/flashcards.html"], "features\\landing\\landing.html": ["../grind/grind.html", "../grind/grind.html", "../grind/grind.html", "../grind/grind.html"], "features\\priority-calculator\\priority-calculator.html": ["../grind/grind.html", "../grind/grind.html", "../study-spaces/study-spaces.html", "../daily-calendar/daily-calendar.html", "../academic-details/academic-details.html", "../extracted/extracted.html", "../subject-marks/subject-marks.html", "../priority-calculator/priority-calculator.html", "../priority-list/priority-list.html"], "features\\priority-list\\priority-list.html": ["../grind/grind.html", "../priority-calculator/priority-calculator.html"], "features\\settings\\settings.html": ["../grind/grind.html", "../grind/grind.html", "../study-spaces/study-spaces.html", "../daily-calendar/daily-calendar.html", "../academic-details/academic-details.html", "../extracted/extracted.html", "../subject-marks/subject-marks.html", "../flashcards/flashcards.html", "../settings/settings.html", "../tasks/tasks.html"], "features\\sleep-saboteurs\\sleep-saboteurs.html": ["../grind/grind.html", "../grind/grind.html", "../study-spaces/study-spaces.html", "../daily-calendar/daily-calendar.html", "../academic-details/academic-details.html", "../extracted/extracted.html", "../subject-marks/subject-marks.html", "../sleep-saboteurs/sleep-saboteurs.html"], "features\\study-spaces\\study-spaces.html": ["../grind/grind.html", "../grind/grind.html", "../study-spaces/study-spaces.html", "../daily-calendar/daily-calendar.html", "../academic-details/academic-details.html", "../extracted/extracted.html", "../subject-marks/subject-marks.html", "../flashcards/flashcards.html"], "features\\subject-marks\\subject-marks.html": ["../grind/grind.html", "../grind/grind.html", "../study-spaces/study-spaces.html", "../daily-calendar/daily-calendar.html", "../academic-details/academic-details.html", "../extracted/extracted.html", "../subject-marks/subject-marks.html", "../flashcards/flashcards.html"], "features\\tasks\\tasks.html": ["../grind/grind.html", "../grind/grind.html", "../study-spaces/study-spaces.html", "../daily-calendar/daily-calendar.html", "../academic-details/academic-details.html", "../extracted/extracted.html", "../subject-marks/subject-marks.html", "../flashcards/flashcards.html"], "features\\workspace\\workspace.html": []}, "broken_links": ["features\\academic-details\\academic-details.html: ../markdown-converter/markdown-converter.html → features\\markdown-converter\\markdown-converter.html"], "external_links": ["features\\academic-details\\academic-details.html: https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css", "features\\academic-details\\academic-details.html: https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css", "features\\daily-calendar\\daily-calendar.html: https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css", "features\\daily-calendar\\daily-calendar.html: https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css", "features\\daily-calendar\\daily-calendar.html: https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css", "features\\extracted\\extracted.html: https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap", "features\\extracted\\extracted.html: https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css", "features\\extracted\\extracted.html: https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css", "features\\flashcards\\flashcards.html: https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css", "features\\flashcards\\flashcards.html: https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css", "features\\flashcards\\flashcards.html: https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css", "features\\flashcards\\flashcards.html: https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js", "features\\flashcards\\flashcards.html: https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css", "features\\flashcards\\flashcards.html: https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css", "features\\flashcards\\flashcards.html: https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css", "features\\grind\\grind.html: https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css", "features\\grind\\grind.html: https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css", "features\\grind\\grind.html: https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css", "features\\grind\\grind.html: https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js", "features\\grind\\grind.html: https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css", "features\\grind\\grind.html: https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css", "features\\grind\\grind.html: https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css", "features\\grind\\grind.html: https://forms.gle/LnMspth72dFMeVV17", "features\\index\\index.html: https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css", "features\\index\\index.html: https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css", "features\\index\\index.html: https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js", "features\\instant-test-feedback\\instant-test-feedback.html: https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css", "features\\instant-test-feedback\\instant-test-feedback.html: https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css", "features\\instant-test-feedback\\instant-test-feedback.html: https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css", "features\\instant-test-feedback\\instant-test-feedback.html: https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js", "features\\instant-test-feedback\\instant-test-feedback.html: https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css", "features\\instant-test-feedback\\instant-test-feedback.html: https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css", "features\\instant-test-feedback\\instant-test-feedback.html: https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css", "features\\instant-test-feedback\\instant-test-feedback.html: https://makersuite.google.com/app/apikey", "features\\landing\\landing.html: https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css", "features\\landing\\landing.html: https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css", "features\\priority-calculator\\priority-calculator.html: https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css", "features\\priority-calculator\\priority-calculator.html: https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css", "features\\priority-list\\priority-list.html: https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css", "features\\priority-list\\priority-list.html: https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css", "features\\settings\\settings.html: https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css", "features\\settings\\settings.html: https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css", "features\\sleep-saboteurs\\sleep-saboteurs.html: https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css", "features\\sleep-saboteurs\\sleep-saboteurs.html: https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css", "features\\study-spaces\\study-spaces.html: https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css", "features\\study-spaces\\study-spaces.html: https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css", "features\\study-spaces\\study-spaces.html: https://makersuite.google.com/app/apikey", "features\\subject-marks\\subject-marks.html: https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css", "features\\subject-marks\\subject-marks.html: https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css", "features\\tasks\\tasks.html: https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css", "features\\tasks\\tasks.html: https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css", "features\\workspace\\workspace.html: https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css", "features\\workspace\\workspace.html: https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css", "features\\workspace\\workspace.html: https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap", "features\\workspace\\workspace.html: https://cdn.quilljs.com/1.3.6/quill.snow.css"]}