{"features": {"academic-details": {"html": "features/academic-details/academic-details.html", "css": "features/academic-details/academic-details.css", "js": "features/academic-details/academic-details.js", "related_files": []}, "daily-calendar": {"html": "features/daily-calendar/daily-calendar.html", "css": "features/daily-calendar/daily-calendar.css", "js": null, "related_files": []}, "extracted": {"html": "features/extracted/extracted.html", "css": "features/extracted/extracted.css", "js": null, "related_files": []}, "flashcards": {"html": "features/flashcards/flashcards.html", "css": "features/flashcards/flashcards.css", "js": "features/flashcards/flashcards.js", "related_files": []}, "grind": {"html": "features/grind/grind.html", "css": null, "js": null, "related_files": []}, "instant-test-feedback": {"html": "features/instant-test-feedback/instant-test-feedback.html", "css": null, "js": null, "related_files": []}, "landing": {"html": "features/landing/landing.html", "css": null, "js": null, "related_files": []}, "priority-calculator": {"html": "features/priority-calculator/priority-calculator.html", "css": "features/priority-calculator/priority-calculator.css", "js": null, "related_files": ["features/priority-calculator/priority-calculator-with-worker.js", "features/priority-calculator/priority-calculator.js"]}, "priority-list": {"html": "features/priority-list/priority-list.html", "css": "features/priority-list/priority-list.css", "js": null, "related_files": []}, "settings": {"html": "features/settings/settings.html", "css": "features/settings/settings.css", "js": null, "related_files": []}, "sleep-saboteurs": {"html": "features/sleep-saboteurs/sleep-saboteurs.html", "css": "features/sleep-saboteurs/sleep-saboteurs.css", "js": null, "related_files": []}, "study-spaces": {"html": "features/study-spaces/study-spaces.html", "css": "features/study-spaces/study-spaces.css", "js": null, "related_files": []}, "subject-marks": {"html": "features/subject-marks/subject-marks.html", "css": "features/subject-marks/subject-marks.css", "js": "features/subject-marks/subject-marks.js", "related_files": []}, "tasks": {"html": "features/tasks/tasks.html", "css": null, "js": null, "related_files": []}, "workspace": {"html": "features/workspace/workspace.html", "css": "features/workspace/workspace.css", "js": null, "related_files": []}, "index": {"html": "features/index/index.html", "css": "features/index/index.css", "js": null, "related_files": []}}, "metadata": {"total_features": 16, "html_files": 17, "css_files": 22, "js_files": 101, "orphaned_files": ["css/ai-search-response.css", "css/alarm-service.css", "css/compact-style.css", "css/notification.css", "css/sideDrawer.css", "css/simulation-enhancer.css", "css/task-display.css", "css/task-notes.css", "css/taskLinks.css", "css/test-feedback.css", "css/text-expansion.css", "js/add-favicon.js", "js/ai-latex-conversion.js", "js/ai-researcher.js", "js/alarm-data-service.js", "js/alarm-handler.js", "js/alarm-mini-display.js", "js/alarm-service-worker.js", "js/alarm-service.js", "js/api-optimization.js", "js/api-settings.js", "js/apiSettingsManager.js", "js/auth.js", "js/calendar-views.js", "js/calendarManager.js", "js/clock-display.js", "js/common-header.js", "js/common.js", "js/cross-tab-sync.js", "js/currentTaskManager.js", "js/data-loader.js", "js/data-sync-integration.js", "js/data-sync-manager.js", "js/energyHologram.js", "js/energyLevels.js", "js/fileViewer.js", "js/firebase-config.js", "js/firebase-init.js", "js/firebaseAuth.js", "js/firebaseConfig.js", "js/firestore-global.js", "js/firestore.js", "js/flashcardManager.js", "js/flashcardTaskIntegration.js", "js/gemini-api.js", "js/googleDriveApi.js", "js/googleGenerativeAI.js", "js/grind-speech-synthesis.js", "js/imageAnalyzer.js", "js/indexedDB.js", "js/initFirestoreData.js", "js/inject-header.js", "js/markdown-converter.js", "js/marks-tracking.js", "js/pandoc-fallback.js", "js/pomodoroGlobal.js", "js/pomodoroTimer.js", "js/priority-list-sorting.js", "js/priority-list-utils.js", "js/priority-sync-fix.js", "js/priority-worker-wrapper.js", "js/quoteManager.js", "js/recipeManager.js", "js/reorganize-scripts.js", "js/roleModelManager.js", "js/scheduleManager.js", "js/semester-management.js", "js/sideDrawer.js", "js/simulation-enhancer.js", "js/sleep-saboteurs-init.js", "js/sleepScheduleManager.js", "js/sleepTimeCalculator.js", "js/sm2.js", "js/soundManager.js", "js/speech-recognition.js", "js/speech-synthesis.js", "js/storageManager.js", "js/studySpaceAnalyzer.js", "js/studySpacesFirestore.js", "js/studySpacesManager.js", "js/subject-management.js", "js/subject-marks-integration.js", "js/subject-marks-ui.js", "js/task-notes-injector.js", "js/task-notes.js", "js/taskAttachments.js", "js/taskFilters.js", "js/taskLinks.js", "js/tasksManager.js", "js/test-feedback.js", "js/text-expansion.js", "js/theme-manager.js", "js/themeManager.js", "js/timetableAnalyzer.js", "js/timetableIntegration.js", "js/todoistIntegration.js", "js/transitionManager.js", "js/ui-utilities.js", "js/update-html-files.js", "js/userGuidance.js", "js/weightage-connector.js", "js/workspace-attachments.js", "js/workspace-core.js", "js/workspace-document.js", "js/workspace-formatting.js", "js/workspace-media.js", "js/workspace-tables-links.js", "js/workspace-ui.js", "js/workspaceFlashcardIntegration.js"], "structure_type": "feature-based", "reorganized": true}, "reorganization_info": {"structure": "feature-based", "base_path": "features/", "reorganized": true, "date": "2024"}}