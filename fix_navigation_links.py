import re
import glob
import os
from pathlib import Path

def fix_navigation_links():
    """
    Fix navigation links in HTML files to work with the new feature-based structure.
    """
    print("🔗 Fixing Navigation Links for Feature-Based Structure")
    print("=" * 60)
    
    html_files = glob.glob("features/*/*.html")
    total_fixes = 0
    
    # Map of HTML files to their feature folders
    feature_map = {}
    for html_file in html_files:
        filename = Path(html_file).name
        feature_name = Path(html_file).parent.name
        feature_map[filename] = feature_name
    
    print(f"📋 Found {len(feature_map)} features:")
    for filename, feature in feature_map.items():
        print(f"   {filename} → features/{feature}/")
    
    for html_file in html_files:
        print(f"\n📝 Processing: {html_file}")
        
        with open(html_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        changes_made = []
        
        # Fix navigation links that point to other HTML files
        # Pattern: href="filename.html" → href="../feature-name/filename.html"
        nav_pattern = r'href="([a-zA-Z0-9-]+\.html)"'
        nav_matches = re.findall(nav_pattern, content)
        
        for match in nav_matches:
            if match in feature_map:
                target_feature = feature_map[match]
                old_href = f'href="{match}"'
                new_href = f'href="../{target_feature}/{match}"'
                
                # Only replace if it's not already in the correct format
                if old_href in content and not f'../{target_feature}/' in content:
                    content = content.replace(old_href, new_href)
                    changes_made.append(f"Navigation: {match} → ../{target_feature}/{match}")
        
        # Fix any remaining absolute paths that start with /js/
        absolute_js_pattern = r'src="/js/([^"]+)"'
        absolute_js_matches = re.findall(absolute_js_pattern, content)
        if absolute_js_matches:
            content = re.sub(absolute_js_pattern, r'src="../../js/\1"', content)
            for match in absolute_js_matches:
                changes_made.append(f"Absolute JS: /js/{match} → ../../js/{match}")
        
        # Write back if changes were made
        if content != original_content:
            with open(html_file, 'w', encoding='utf-8') as f:
                f.write(content)
            
            total_fixes += len(changes_made)
            print(f"   ✅ Fixed {len(changes_made)} references:")
            for change in changes_made:
                print(f"      {change}")
        else:
            print(f"   ℹ️  No changes needed")
    
    return total_fixes

def create_navigation_test_report():
    """
    Create a report showing all navigation links and their targets.
    """
    print("\n📊 Creating Navigation Test Report")
    print("=" * 60)
    
    html_files = glob.glob("features/*/*.html")
    nav_report = {
        "navigation_links": {},
        "broken_links": [],
        "external_links": []
    }
    
    for html_file in html_files:
        with open(html_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Find all href links
        href_pattern = r'href="([^"]+)"'
        href_matches = re.findall(href_pattern, content)
        
        nav_links = []
        for href in href_matches:
            if href.startswith('#') or href.startswith('javascript:') or href.startswith('mailto:'):
                continue  # Skip anchors, javascript, and mailto links
            elif href.startswith('http'):
                nav_report["external_links"].append(f"{html_file}: {href}")
            elif href.endswith('.html'):
                nav_links.append(href)
                
                # Check if target exists
                if href.startswith('../'):
                    # Relative path to another feature
                    target_path = Path(html_file).parent.parent / href[3:]
                else:
                    # Same directory
                    target_path = Path(html_file).parent / href
                
                if not target_path.exists():
                    nav_report["broken_links"].append(f"{html_file}: {href} → {target_path}")
        
        nav_report["navigation_links"][html_file] = nav_links
    
    return nav_report

if __name__ == "__main__":
    print("🔗 Agent 3: Navigation Links Fixing")
    print("=" * 60)
    
    # Fix navigation links
    nav_fixes = fix_navigation_links()
    
    # Create navigation test report
    nav_report = create_navigation_test_report()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 NAVIGATION FIXING SUMMARY")
    print("=" * 60)
    print(f"✅ Navigation fixes applied: {nav_fixes}")
    print(f"🔗 Total navigation links found: {sum(len(links) for links in nav_report['navigation_links'].values())}")
    print(f"🌐 External links found: {len(nav_report['external_links'])}")
    print(f"❌ Broken links found: {len(nav_report['broken_links'])}")
    
    if nav_report['broken_links']:
        print(f"\n❌ Broken links to review:")
        for broken_link in nav_report['broken_links'][:5]:  # Show first 5
            print(f"   {broken_link}")
        if len(nav_report['broken_links']) > 5:
            print(f"   ... and {len(nav_report['broken_links']) - 5} more")
    
    # Save navigation report
    import json
    with open('navigation_report.json', 'w') as f:
        json.dump(nav_report, f, indent=2)
    
    print(f"\n📄 Navigation report saved as navigation_report.json")
    print(f"🎉 Navigation links fixing complete!")
