# 🛠️ Agent 3: Relative-Path Rewiring
# Goal: Batch-update <link> & <script> tags and import statements

Write-Host "🛠️  Agent 3: Relative-Path Rewiring" -ForegroundColor Green
Write-Host "Goal: Batch-update <link> & <script> tags and import statements" -ForegroundColor Yellow
Write-Host "=" * 60

$htmlFixed = @()
$jsFixed = @()
$cssFixed = @()

# Function to fix HTML files
function Fix-HtmlPaths {
    Write-Host "`n🛠️  Fixing HTML file paths..." -ForegroundColor Cyan
    Write-Host "=" * 60
    
    $htmlFiles = Get-ChildItem "features" -Recurse -Filter "*.html"
    
    foreach ($htmlFile in $htmlFiles) {
        Write-Host "`n📝 Processing: $($htmlFile.FullName)" -ForegroundColor White
        
        $content = Get-Content $htmlFile.FullName -Raw -Encoding UTF8
        $originalContent = $content
        $changesMade = @()
        
        # Fix CSS links: href="css/filename.css" -> href="./filename.css"
        $cssMatches = [regex]::Matches($content, 'href="css/([^"]+\.css)"')
        if ($cssMatches.Count -gt 0) {
            $content = $content -replace 'href="css/([^"]+\.css)"', 'href="./$1"'
            foreach ($match in $cssMatches) {
                $changesMade += "CSS: css/$($match.Groups[1].Value) → ./$($match.Groups[1].Value)"
            }
        }
        
        # Fix CSS links with ../: href="../css/filename.css" -> href="./filename.css"
        $cssMatches2 = [regex]::Matches($content, 'href="\.\./css/([^"]+\.css)"')
        if ($cssMatches2.Count -gt 0) {
            $content = $content -replace 'href="\.\./css/([^"]+\.css)"', 'href="./$1"'
            foreach ($match in $cssMatches2) {
                $changesMade += "CSS: ../css/$($match.Groups[1].Value) → ./$($match.Groups[1].Value)"
            }
        }
        
        # Fix JS scripts: src="js/filename.js" -> src="./filename.js"
        $jsMatches = [regex]::Matches($content, 'src="js/([^"]+\.js)"')
        if ($jsMatches.Count -gt 0) {
            $content = $content -replace 'src="js/([^"]+\.js)"', 'src="./$1"'
            foreach ($match in $jsMatches) {
                $changesMade += "JS: js/$($match.Groups[1].Value) → ./$($match.Groups[1].Value)"
            }
        }
        
        # Fix JS scripts with ../: src="../js/filename.js" -> src="./filename.js"
        $jsMatches2 = [regex]::Matches($content, 'src="\.\./js/([^"]+\.js)"')
        if ($jsMatches2.Count -gt 0) {
            $content = $content -replace 'src="\.\./js/([^"]+\.js)"', 'src="./$1"'
            foreach ($match in $jsMatches2) {
                $changesMade += "JS: ../js/$($match.Groups[1].Value) → ./$($match.Groups[1].Value)"
            }
        }
        
        # Fix styles directory references: href="styles/filename.css" -> href="../../styles/filename.css"
        $stylesMatches = [regex]::Matches($content, 'href="styles/([^"]+\.css)"')
        if ($stylesMatches.Count -gt 0) {
            $content = $content -replace 'href="styles/([^"]+\.css)"', 'href="../../styles/$1"'
            foreach ($match in $stylesMatches) {
                $changesMade += "Styles: styles/$($match.Groups[1].Value) → ../../styles/$($match.Groups[1].Value)"
            }
        }
        
        # Fix root-level asset references
        $rootAssets = @('pop.mp3', 'worker.js', 'test-worker.js')
        foreach ($asset in $rootAssets) {
            if ($content -match "src=`"$asset`"") {
                $content = $content -replace "src=`"$asset`"", "src=`"../../$asset`""
                $changesMade += "Root asset: $asset → ../../$asset"
            }
        }
        
        # Write back if changes were made
        if ($content -ne $originalContent) {
            Set-Content -Path $htmlFile.FullName -Value $content -Encoding UTF8
            $script:htmlFixed += $htmlFile.FullName
            Write-Host "   ✅ Fixed $($changesMade.Count) references:" -ForegroundColor Green
            foreach ($change in $changesMade) {
                Write-Host "      $change" -ForegroundColor White
            }
        } else {
            Write-Host "   ℹ️  No changes needed" -ForegroundColor Gray
        }
    }
}

# Function to fix JavaScript files
function Fix-JsPaths {
    Write-Host "`n🔧 Fixing JavaScript imports and requires..." -ForegroundColor Cyan
    Write-Host "=" * 60
    
    $jsFiles = Get-ChildItem "features" -Recurse -Filter "*.js"
    
    foreach ($jsFile in $jsFiles) {
        Write-Host "`n⚡ Processing: $($jsFile.FullName)" -ForegroundColor White
        
        $content = Get-Content $jsFile.FullName -Raw -Encoding UTF8
        $originalContent = $content
        $changesMade = @()
        
        # Fix require statements: require('../js/filename') -> require('./filename')
        $requireMatches = [regex]::Matches($content, "require\(['\`"]\.\.\/js\/([^'\`"]+)['`\"]\)")
        if ($requireMatches.Count -gt 0) {
            $content = $content -replace "require\(['\`"]\.\.\/js\/([^'\`"]+)['`\"]\)", "require('./$1')"
            foreach ($match in $requireMatches) {
                $changesMade += "Require: ../js/$($match.Groups[1].Value) → ./$($match.Groups[1].Value)"
            }
        }
        
        # Fix import statements: import ... from '../js/filename' -> import ... from './filename'
        $importMatches = [regex]::Matches($content, "import\s+.*\s+from\s+['\`"]\.\.\/js\/([^'\`"]+)['\`"]")
        if ($importMatches.Count -gt 0) {
            $content = $content -replace "from\s+['\`"]\.\.\/js\/([^'\`"]+)['\`"]", "from './$1'"
            foreach ($match in $importMatches) {
                $changesMade += "Import: ../js/$($match.Groups[1].Value) → ./$($match.Groups[1].Value)"
            }
        }
        
        # Fix dynamic imports: import('../js/filename') -> import('./filename')
        $dynamicMatches = [regex]::Matches($content, "import\(['\`"]\.\.\/js\/([^'\`"]+)['`\"]\)")
        if ($dynamicMatches.Count -gt 0) {
            $content = $content -replace "import\(['\`"]\.\.\/js\/([^'\`"]+)['`\"]\)", "import('./$1')"
            foreach ($match in $dynamicMatches) {
                $changesMade += "Dynamic import: ../js/$($match.Groups[1].Value) → ./$($match.Groups[1].Value)"
            }
        }
        
        # Fix absolute js/ references: 'js/filename.js' -> '../../js/filename.js'
        $absMatches = [regex]::Matches($content, "['\`"]js\/([^'\`"]+\.js)['\`"]")
        if ($absMatches.Count -gt 0) {
            $content = $content -replace "['\`"]js\/([^'\`"]+\.js)['\`"]", "'../../js/$1'"
            foreach ($match in $absMatches) {
                $changesMade += "Absolute JS: js/$($match.Groups[1].Value) → ../../js/$($match.Groups[1].Value)"
            }
        }
        
        # Write back if changes were made
        if ($content -ne $originalContent) {
            Set-Content -Path $jsFile.FullName -Value $content -Encoding UTF8
            $script:jsFixed += $jsFile.FullName
            Write-Host "   ✅ Fixed $($changesMade.Count) references:" -ForegroundColor Green
            foreach ($change in $changesMade) {
                Write-Host "      $change" -ForegroundColor White
            }
        } else {
            Write-Host "   ℹ️  No changes needed" -ForegroundColor Gray
        }
    }
}

# Function to fix CSS files
function Fix-CssPaths {
    Write-Host "`n🎨 Fixing CSS imports and references..." -ForegroundColor Cyan
    Write-Host "=" * 60
    
    $cssFiles = Get-ChildItem "features" -Recurse -Filter "*.css"
    
    foreach ($cssFile in $cssFiles) {
        Write-Host "`n🎨 Processing: $($cssFile.FullName)" -ForegroundColor White
        
        $content = Get-Content $cssFile.FullName -Raw -Encoding UTF8
        $originalContent = $content
        $changesMade = @()
        
        # Fix @import statements: @import '../css/filename' -> @import './filename'
        $importMatches = [regex]::Matches($content, "@import\s+['\`"]\.\.\/css\/([^'\`"]+)['\`"]")
        if ($importMatches.Count -gt 0) {
            $content = $content -replace "@import\s+['\`"]\.\.\/css\/([^'\`"]+)['\`"]", "@import './$1'"
            foreach ($match in $importMatches) {
                $changesMade += "@import: ../css/$($match.Groups[1].Value) → ./$($match.Groups[1].Value)"
            }
        }
        
        # Fix url() references: url('../filename') -> url('../../filename')
        $urlMatches = [regex]::Matches($content, "url\(['\`"]?\.\.\/([^'\`"]+)['\`"]?\)")
        if ($urlMatches.Count -gt 0) {
            $content = $content -replace "url\(['\`"]?\.\.\/([^'\`"]+)['\`"]?\)", "url('../../$1')"
            foreach ($match in $urlMatches) {
                $changesMade += "url(): ../$($match.Groups[1].Value) → ../../$($match.Groups[1].Value)"
            }
        }
        
        # Write back if changes were made
        if ($content -ne $originalContent) {
            Set-Content -Path $cssFile.FullName -Value $content -Encoding UTF8
            $script:cssFixed += $cssFile.FullName
            Write-Host "   ✅ Fixed $($changesMade.Count) references:" -ForegroundColor Green
            foreach ($change in $changesMade) {
                Write-Host "      $change" -ForegroundColor White
            }
        } else {
            Write-Host "   ℹ️  No changes needed" -ForegroundColor Gray
        }
    }
}

# Execute the fixes
Fix-HtmlPaths
Fix-JsPaths
Fix-CssPaths

# Summary
Write-Host "`n" + "=" * 60 -ForegroundColor White
Write-Host "📊 PATH REWIRING SUMMARY" -ForegroundColor Green
Write-Host "=" * 60 -ForegroundColor White
Write-Host "✅ HTML files fixed: $($htmlFixed.Count)" -ForegroundColor Green
Write-Host "✅ JS files fixed: $($jsFixed.Count)" -ForegroundColor Green
Write-Host "✅ CSS files fixed: $($cssFixed.Count)" -ForegroundColor Green

if ($htmlFixed.Count -gt 0) {
    Write-Host "`n📄 HTML files updated:" -ForegroundColor Blue
    foreach ($file in $htmlFixed) {
        Write-Host "   $file" -ForegroundColor White
    }
}

if ($jsFixed.Count -gt 0) {
    Write-Host "`n⚡ JavaScript files updated:" -ForegroundColor Yellow
    foreach ($file in $jsFixed) {
        Write-Host "   $file" -ForegroundColor White
    }
}

if ($cssFixed.Count -gt 0) {
    Write-Host "`n🎨 CSS files updated:" -ForegroundColor Magenta
    foreach ($file in $cssFixed) {
        Write-Host "   $file" -ForegroundColor White
    }
}

Write-Host "`n🎉 Path rewiring complete! All references should now work correctly." -ForegroundColor Green
