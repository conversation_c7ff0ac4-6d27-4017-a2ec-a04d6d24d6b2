import os
import json
import shutil
from pathlib import Path

def create_feature_folders_and_move():
    """
    Bulk folder creation and file moves to reorganize app into feature-based structure.
    Creates /features/* folders and moves HTML/CSS/JS files accordingly.
    """
    
    # Load the catalog
    with open('catalog.json', 'r') as f:
        catalog = json.load(f)
    
    # Create features directory if it doesn't exist
    features_dir = Path("features")
    features_dir.mkdir(exist_ok=True)
    
    moved_files = []
    errors = []
    
    print("🔀 Starting bulk folder creation and file moves...")
    print("=" * 60)
    
    # Process each feature
    for feature_name, feature_data in catalog["features"].items():
        print(f"\n📁 Processing feature: {feature_name}")
        
        # Create feature directory
        feature_dir = features_dir / feature_name
        feature_dir.mkdir(exist_ok=True)
        print(f"   ✅ Created directory: features/{feature_name}")
        
        # Move HTML file
        if feature_data["html"]:
            html_src = Path(feature_data["html"])
            html_dst = feature_dir / html_src.name
            
            if html_src.exists():
                try:
                    shutil.move(str(html_src), str(html_dst))
                    moved_files.append(f"{html_src} → {html_dst}")
                    print(f"   📄 Moved HTML: {html_src.name}")
                except Exception as e:
                    errors.append(f"Failed to move {html_src}: {e}")
                    print(f"   ❌ Error moving {html_src}: {e}")
            else:
                print(f"   ⚠️  HTML file not found: {html_src}")
        
        # Move CSS file
        if feature_data["css"]:
            css_src = Path(feature_data["css"])
            css_dst = feature_dir / css_src.name
            
            if css_src.exists():
                try:
                    shutil.move(str(css_src), str(css_dst))
                    moved_files.append(f"{css_src} → {css_dst}")
                    print(f"   🎨 Moved CSS: {css_src.name}")
                except Exception as e:
                    errors.append(f"Failed to move {css_src}: {e}")
                    print(f"   ❌ Error moving {css_src}: {e}")
            else:
                print(f"   ⚠️  CSS file not found: {css_src}")
        
        # Move JS file
        if feature_data["js"]:
            js_src = Path(feature_data["js"])
            js_dst = feature_dir / js_src.name
            
            if js_src.exists():
                try:
                    shutil.move(str(js_src), str(js_dst))
                    moved_files.append(f"{js_src} → {js_dst}")
                    print(f"   ⚡ Moved JS: {js_src.name}")
                except Exception as e:
                    errors.append(f"Failed to move {js_src}: {e}")
                    print(f"   ❌ Error moving {js_src}: {e}")
            else:
                print(f"   ⚠️  JS file not found: {js_src}")
    
    return moved_files, errors

def move_workers_and_related():
    """
    Move worker files and other related scripts to appropriate feature folders.
    """
    print("\n🔧 Processing worker files and related scripts...")
    print("=" * 60)
    
    moved_workers = []
    errors = []
    
    # Check if workers directory exists
    workers_dir = Path("workers")
    if workers_dir.exists():
        worker_files = list(workers_dir.glob("*.js"))
        print(f"Found {len(worker_files)} worker files")
        
        for worker_file in worker_files:
            # Try to match worker to feature based on name
            worker_name = worker_file.stem
            
            # Look for matching feature
            features_dir = Path("features")
            matched = False
            
            for feature_dir in features_dir.iterdir():
                if feature_dir.is_dir():
                    feature_name = feature_dir.name
                    
                    # Check if worker name contains feature name or vice versa
                    if (feature_name in worker_name.lower() or 
                        worker_name.lower() in feature_name or
                        worker_name.lower().replace('-', '') in feature_name.replace('-', '')):
                        
                        try:
                            dst = feature_dir / worker_file.name
                            shutil.move(str(worker_file), str(dst))
                            moved_workers.append(f"{worker_file} → {dst}")
                            print(f"   🔧 Moved worker: {worker_file.name} → features/{feature_name}/")
                            matched = True
                            break
                        except Exception as e:
                            errors.append(f"Failed to move {worker_file}: {e}")
                            print(f"   ❌ Error moving {worker_file}: {e}")
            
            if not matched:
                print(f"   ⚠️  No matching feature found for worker: {worker_file.name}")
    
    # Move other standalone files that might belong to features
    standalone_files = [
        "priority-calculator.js",
        "priority-calculator-with-worker.js", 
        "worker.js",
        "test-worker.js"
    ]
    
    for file_name in standalone_files:
        file_path = Path(file_name)
        if file_path.exists():
            # Try to match to a feature
            base_name = file_name.replace('.js', '').replace('-with-worker', '').replace('test-', '')
            
            features_dir = Path("features")
            for feature_dir in features_dir.iterdir():
                if feature_dir.is_dir() and base_name in feature_dir.name:
                    try:
                        dst = feature_dir / file_name
                        shutil.move(str(file_path), str(dst))
                        moved_workers.append(f"{file_path} → {dst}")
                        print(f"   📦 Moved standalone: {file_name} → features/{feature_dir.name}/")
                        break
                    except Exception as e:
                        errors.append(f"Failed to move {file_path}: {e}")
                        print(f"   ❌ Error moving {file_path}: {e}")
    
    return moved_workers, errors

if __name__ == "__main__":
    print("🚀 Agent 2: Bulk Folder Creation & Moves")
    print("Goal: Scaffold /features/*, move files, preserve structure")
    print("=" * 60)
    
    # Step 1: Create feature folders and move main files
    moved_files, file_errors = create_feature_folders_and_move()
    
    # Step 2: Move workers and related scripts
    moved_workers, worker_errors = move_workers_and_related()
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 REORGANIZATION SUMMARY")
    print("=" * 60)
    print(f"✅ Files moved: {len(moved_files + moved_workers)}")
    print(f"❌ Errors: {len(file_errors + worker_errors)}")
    
    if moved_files:
        print(f"\n📄 Main files moved ({len(moved_files)}):")
        for move in moved_files[:10]:  # Show first 10
            print(f"   {move}")
        if len(moved_files) > 10:
            print(f"   ... and {len(moved_files) - 10} more")
    
    if moved_workers:
        print(f"\n🔧 Workers/related moved ({len(moved_workers)}):")
        for move in moved_workers:
            print(f"   {move}")
    
    if file_errors or worker_errors:
        print(f"\n❌ Errors encountered:")
        for error in (file_errors + worker_errors):
            print(f"   {error}")
    
    print(f"\n🎉 Reorganization complete! Check the features/ directory.")
