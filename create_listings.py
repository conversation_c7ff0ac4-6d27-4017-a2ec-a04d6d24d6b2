import os

def create_file_listings():
    """Create individual listing files for HTML, CSS, and JS files"""
    
    # List all HTML files
    html_files = []
    for file in os.listdir("."):
        if file.endswith(".html"):
            html_files.append(file)
    
    with open("features.txt", "w") as f:
        for html_file in sorted(html_files):
            f.write(html_file + "\n")
    
    # List all CSS files
    css_files = []
    css_dir = "css"
    if os.path.exists(css_dir):
        for file in os.listdir(css_dir):
            if file.endswith(".css"):
                css_files.append(file)
    
    with open("styles.txt", "w") as f:
        for css_file in sorted(css_files):
            f.write(css_file + "\n")
    
    # List all JS files
    js_files = []
    js_dir = "js"
    if os.path.exists(js_dir):
        for file in os.listdir(js_dir):
            if file.endswith(".js"):
                js_files.append(file)
    
    with open("scripts.txt", "w") as f:
        for js_file in sorted(js_files):
            f.write(js_file + "\n")
    
    print(f"Created listings:")
    print(f"- features.txt: {len(html_files)} HTML files")
    print(f"- styles.txt: {len(css_files)} CSS files") 
    print(f"- scripts.txt: {len(js_files)} JS files")

if __name__ == "__main__":
    create_file_listings()
