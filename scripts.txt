academic-details.js
add-favicon.js
ai-latex-conversion.js
ai-researcher.js
alarm-data-service.js
alarm-handler.js
alarm-mini-display.js
alarm-service-worker.js
alarm-service.js
api-optimization.js
api-settings.js
apiSettingsManager.js
auth.js
calendar-views.js
calendarManager.js
clock-display.js
common-header.js
common.js
cross-tab-sync.js
currentTaskManager.js
data-loader.js
data-sync-integration.js
data-sync-manager.js
energyHologram.js
energyLevels.js
fileViewer.js
firebase-config.js
firebase-init.js
firebaseAuth.js
firebaseConfig.js
firestore-global.js
firestore.js
flashcardManager.js
flashcardTaskIntegration.js
flashcards.js
gemini-api.js
googleDriveApi.js
googleGenerativeAI.js
grind-speech-synthesis.js
imageAnalyzer.js
indexedDB.js
initFirestoreData.js
inject-header.js
markdown-converter.js
marks-tracking.js
pandoc-fallback.js
pomodoroGlobal.js
pomodoroTimer.js
priority-list-sorting.js
priority-list-utils.js
priority-sync-fix.js
priority-worker-wrapper.js
quoteManager.js
recipeManager.js
reorganize-scripts.js
roleModelManager.js
scheduleManager.js
semester-management.js
sideDrawer.js
simulation-enhancer.js
sleep-saboteurs-init.js
sleepScheduleManager.js
sleepTimeCalculator.js
sm2.js
soundManager.js
speech-recognition.js
speech-synthesis.js
storageManager.js
studySpaceAnalyzer.js
studySpacesFirestore.js
studySpacesManager.js
subject-management.js
subject-marks-integration.js
subject-marks-ui.js
subject-marks.js
task-notes-injector.js
task-notes.js
taskAttachments.js
taskFilters.js
taskLinks.js
tasksManager.js
test-feedback.js
text-expansion.js
theme-manager.js
themeManager.js
timetableAnalyzer.js
timetableIntegration.js
todoistIntegration.js
transitionManager.js
ui-utilities.js
update-html-files.js
userGuidance.js
weightage-connector.js
workspace-attachments.js
workspace-core.js
workspace-document.js
workspace-formatting.js
workspace-media.js
workspace-tables-links.js
workspace-ui.js
workspaceFlashcardIntegration.js
