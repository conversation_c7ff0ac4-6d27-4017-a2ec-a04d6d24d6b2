<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GPAce - Priority List</title>
    <!-- Favicon -->
    <link rel="icon" type="image/png" href="../../assets/images/gpace-logo-white.png">
    <link rel="shortcut icon" type="image/png" href="../../assets/images/gpace-logo-white.png">

    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Side Drawer Styles -->
    <link href="./sideDrawer.css" rel="stylesheet">
    <!-- Priority List Styles -->
    <link href="./priority-list.css" rel="stylesheet">
    <!-- Data Initialization -->

    <!-- Common Initialization -->


    <!-- Priority Sync Fix -->

    <!-- Priority List Sorting -->

    <!-- Priority List Utils -->

</head>

<body>
    <!-- Navigation Bar -->
    <nav class="top-nav">
        <div class="nav-brand d-flex align-items-center">
            <img src="../../assets/images/gpace-logo-white.png" alt="GPAce Logo" style="height: 80px; margin-right: 0px;">
            <a href="../grind/grind.html" style="text-decoration: none; color: inherit;">GPAce</a>
        </div>
    </nav>
    <div class="container mt-4">
        <div class="list-header">
            <div class="priority-score">Priority</div>
            <div class="task-title">Task</div>
            <div class="task-section">Section</div>
            <div class="project-name">Project</div>
            <div class="due-date">Due Date</div>
        </div>

        <div class="task-navigation">
            <button onclick="navigateTask('prev')" class="nav-arrow" title="Previous Task">
                <i class="bi bi-chevron-left"></i>
            </button>
            <span class="navigation-text">Navigate Tasks</span>
            <button onclick="navigateTask('next')" class="nav-arrow" title="Next Task">
                <i class="bi bi-chevron-right"></i>
            </button>
        </div>

        <div id="taskList">
            <!-- Tasks will be populated here -->
        </div>
    </div>

    <a href="../priority-calculator/priority-calculator.html" class="back-button">
        <i class="bi bi-arrow-left"></i> Back to Calculator
    </a>

    <!-- All JavaScript has been moved to js/priority-list-utils.js -->






</body>

</html>






</body>

</html>
    <!-- Scripts -->
    <!-- Firebase Initialization -->
    <script type="module">
        import { initializeApp } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js';
        import { getFirestore } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js';
        import { getAuth } from 'https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js';
        import { firebaseConfig } from './js/firebaseConfig.js';

        // Initialize Firebase safely
        let app;
        try {
            app = initializeApp(firebaseConfig);
            console.log("Firebase initialized successfully");

            // Set up global variables
            window.db = getFirestore(app);
            window.auth = getAuth(app);
        } catch (e) {
            if (e.code === 'app/duplicate-app') {
                console.log("Firebase already initialized, using existing app");
                try {
                    app = initializeApp();
                    window.db = getFirestore(app);
                    window.auth = getAuth(app);
                } catch(getAppError) {
                    console.error("Could not get existing Firebase app instance.", getAppError);
                }
            } else {
                console.error("Firebase initialization error:", e);
            }
        }
    </script>

    <!-- Authentication Setup -->
    <script type="module">
        import { auth as importedAuth, signInWithGoogle, signOutUser, initializeAuth } from './js/auth.js';
        window.auth = window.auth || importedAuth;
        window.signInWithGoogle = signInWithGoogle;
        window.signOutUser = signOutUser;

        // Initialize authentication
        document.addEventListener('DOMContentLoaded', () => {
            initializeAuth();
        });
    </script>

    <!-- Firestore Data Operations -->
    <script type="module">
        import { initializeFirestoreData } from './js/initFirestoreData.js';
        window.initializeFirestoreData = initializeFirestoreData;

        // Initialize data when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            setTimeout(() => {
                if (typeof window.initializeFirestoreData === 'function') {
                    window.initializeFirestoreData();
                }
            }, 1500);
        });
    </script>

    <script type="module" src="./common.js"></script>
    <script type="module" src="./cross-tab-sync.js"></script>
    <script type="module" src="./priority-sync-fix.js"></script>
    <script src="./priority-list-sorting.js"></script>
    <script type="module" src="./priority-list-utils.js"></script>
    <script src="./sideDrawer.js"></script>
    <script src="../../js/inject-header.js"></script>
</body>
</html>