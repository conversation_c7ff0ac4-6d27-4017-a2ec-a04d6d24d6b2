{"html_files": {"features\\academic-details\\academic-details.html": {"css_references": ["https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css", "https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css", "./sideDrawer.css", "./academic-details.css"], "js_references": ["./academic-details.js", "./cross-tab-sync.js", "https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js", "./sideDrawer.js", "./subject-management.js", "./ui-utilities.js", "./semester-management.js", "./auth.js", "./firestore.js", "../../js/inject-header.js"], "asset_references": ["../../assets/images/gpace-logo-white.png", "../../assets/images/gpace-logo-white.png", "../../assets/images/gpace-logo-white.png"]}, "features\\daily-calendar\\daily-calendar.html": {"css_references": ["https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css", "https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css", "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css", "main.css", "./sideDrawer.css", "./daily-calendar.css", "./task-display.css"], "js_references": ["./sideDrawer.js", "./firebaseAuth.js", "./sleepScheduleManager.js", "./calendarManager.js", "./timetableIntegration.js", "./currentTaskManager.js", "./cross-tab-sync.js", "../../js/inject-header.js"], "asset_references": ["../../assets/images/gpace-logo-white.png", "../../assets/images/gpace-logo-white.png", "../../assets/images/gpace-logo-white.png"]}, "features\\extracted\\extracted.html": {"css_references": ["https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css", "https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css", "./sideDrawer.css", "./extracted.css"], "js_references": ["./sideDrawer.js", "./cross-tab-sync.js", "https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js", "../../js/inject-header.js"], "asset_references": ["../../assets/images/gpace-logo-white.png", "../../assets/images/gpace-logo-white.png", "../../assets/images/gpace-logo-white.png"]}, "features\\flashcards\\flashcards.html": {"css_references": ["https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css", "https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css", "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css", "main.css", "./sideDrawer.css", "https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css", "https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css", "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css", "main.css", "./sideDrawer.css", "grind.css", "./flashcards.css"], "js_references": ["https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js", "./storageManager.js", "./common.js", "./cross-tab-sync.js", "./sm2.js", "./flashcards.js", "./sideDrawer.js", "../../js/inject-header.js"], "asset_references": ["../../assets/images/gpace-logo-white.png", "../../assets/images/gpace-logo-white.png", "../../assets/images/gpace-logo-white.png"]}, "features\\grind\\grind.html": {"css_references": ["https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css", "https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css", "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css", "main.css", "./sideDrawer.css", "./taskLinks.css", "./search-modal.css", "https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css", "https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css", "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css", "main.css", "./sideDrawer.css", "grind.css", "./task-display.css", "./text-expansion.css", "./simulation-enhancer.css", "./ai-search-response.css", "./task-notes.css"], "js_references": ["https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js", "./ai-latex-conversion.js", "https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js", "./cross-tab-sync.js", "./initFirestoreData.js", "./common.js", "./userGuidance.js", "https://unpkg.com/es-module-shims@1.8.0/dist/es-module-shims.js", "https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.8/dist/umd/popper.min.js", "https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js", "https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js", "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js", "https://cdn.jsdelivr.net/npm/chart.js", "https://kit.fontawesome.com/51198d7b97.js", "https://cdn.jsdelivr.net/npm/es-module-shims", "https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js", "./storageManager.js", "./grind-speech-synthesis.js", "./taskLinks.js", "./currentTaskManager.js", "priority-calculator.js", "./sleepTimeCalculator.js", "./energyLevels.js", "./sideDrawer.js", "./pomodoroTimer.js", "./task-notes-injector.js", "./task-notes.js", "./text-expansion.js", "./cross-tab-sync.js", "./initFirestoreData.js", "./common.js", "./ai-researcher.js", "./firebase-init.js", "../../js/inject-header.js"], "asset_references": ["../../assets/images/gpace-logo-white.png", "../../assets/images/gpace-logo-white.png", "../../assets/images/gpace-logo-white.png"]}, "features\\index\\index.html": {"css_references": ["https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css", "https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css", "./alarm-service.css"], "js_references": ["./cacheManager.js", "./cross-tab-sync.js", "./alarm-service.js", "./alarm-mini-display.js", "../../js/inject-header.js"], "asset_references": ["../../assets/images/gpace-logo-white.png", "../../assets/images/gpace-logo-white.png", "../../assets/images/gpace-logo-white.png"]}, "features\\instant-test-feedback\\instant-test-feedback.html": {"css_references": ["https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css", "https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css", "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css", "main.css", "./sideDrawer.css", "https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css", "https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css", "https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css", "main.css", "./sideDrawer.css", "grind.css", "./test-feedback.css"], "js_references": ["https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js", "https://cdn.jsdelivr.net/npm/marked/marked.min.js", "https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js", "https://cdnjs.cloudflare.com/ajax/libs/html2canvas/1.4.1/html2canvas.min.js", "./api-settings.js", "./api-optimization.js", "./test-feedback.js", "./gemini-api.js", "./cross-tab-sync.js", "./sideDrawer.js", "../../js/inject-header.js"], "asset_references": ["../../assets/images/gpace-logo-white.png", "../../assets/images/gpace-logo-white.png", "../../assets/images/gpace-logo-white.png"]}, "features\\landing\\landing.html": {"css_references": ["https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css", "https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css", "../../styles/main.css", "./sideDrawer.css"], "js_references": ["./theme-toggle.js", "./cross-tab-sync.js", "../../js/inject-header.js"], "asset_references": ["../../assets/images/gpace-logo-white.png", "../../assets/images/gpace-logo-white.png", "../../assets/images/gpace-logo-white.png", "../../assets/images/gpace-logo-white.png"]}, "features\\priority-calculator\\priority-calculator.html": {"css_references": ["https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css", "https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css", "./sideDrawer.css", "./priority-calculator.css"], "js_references": ["https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js", "./cross-tab-sync.js", "./common.js", "priority-calculator.js", "./sideDrawer.js"], "asset_references": ["../../assets/images/gpace-logo-white.png", "../../assets/images/gpace-logo-white.png", "../../assets/images/gpace-logo-white.png"]}, "features\\priority-list\\priority-list.html": {"css_references": ["https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css", "https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css", "./sideDrawer.css", "./priority-list.css"], "js_references": ["./common.js", "./cross-tab-sync.js", "./priority-sync-fix.js", "./priority-list-sorting.js", "./priority-list-utils.js", "./sideDrawer.js", "../../js/inject-header.js"], "asset_references": ["../../assets/images/gpace-logo-white.png", "../../assets/images/gpace-logo-white.png", "../../assets/images/gpace-logo-white.png"]}, "features\\settings\\settings.html": {"css_references": ["https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css", "https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css", "./settings.css"], "js_references": ["./soundManager.js", "./transitionManager.js", "./todoistIntegration.js", "./app.js", "https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js", "https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js", "./gemini-api.js", "./themeManager.js", "./quoteManager.js", "./roleModelManager.js", "../../js/inject-header.js"], "asset_references": ["../../assets/images/gpace-logo-white.png", "../../assets/images/gpace-logo-white.png", "../../assets/images/gpace-logo-white.png"]}, "features\\sleep-saboteurs\\sleep-saboteurs.html": {"css_references": ["https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css", "https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css", "./sideDrawer.css", "./alarm-service.css", "./sleep-saboteurs.css"], "js_references": ["https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js", "./alarm-service.js", "./clock-display.js", "./theme-manager.js", "./sleep-saboteurs-init.js", "./cross-tab-sync.js", "./sideDrawer.js", "../../js/inject-header.js"], "asset_references": ["../../assets/images/gpace-logo-white.png", "../../assets/images/gpace-logo-white.png", "../../assets/images/gpace-logo-white.png"]}, "features\\study-spaces\\study-spaces.html": {"css_references": ["https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css", "https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css", "main.css", "./sideDrawer.css", "./study-spaces.css"], "js_references": ["https://cdn.jsdelivr.net/npm/@popperjs/core@2.11.8/dist/umd/popper.min.js", "https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js", "/socket.io/socket.io.js", "./timetableAnalyzer.js", "./studySpacesManager.js", "./imageAnalyzer.js", "./scheduleManager.js", "./sideDrawer.js", "https://www.gstatic.com/firebasejs/8.10.0/firebase-app.js", "https://www.gstatic.com/firebasejs/8.10.0/firebase-functions.js", "./firebaseAuth.js", "./apiSettingsManager.js", "./studySpaceAnalyzer.js", "./cross-tab-sync.js", "../../js/inject-header.js"], "asset_references": ["../../assets/images/gpace-logo-white.png", "../../assets/images/gpace-logo-white.png", "../../assets/images/gpace-logo-white.png"]}, "features\\subject-marks\\subject-marks.html": {"css_references": ["https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css", "https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css", "./sideDrawer.css", "./subject-marks.css"], "js_references": ["./firebase-init.js", "./firestore-global.js", "./subject-marks-integration.js", "./data-sync-integration.js", "./cross-tab-sync.js", "https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js", "./subject-marks-ui.js", "./sideDrawer.js", "../../js/inject-header.js"], "asset_references": ["../../assets/images/gpace-logo-white.png", "../../assets/images/gpace-logo-white.png", "../../assets/images/gpace-logo-white.png"]}, "features\\tasks\\tasks.html": {"css_references": ["https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css", "https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css", "./sideDrawer.css"], "js_references": ["https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js", "./todoistIntegration.js", "./sideDrawer.js", "./tasksManager.js", "./taskFilters.js", "./cross-tab-sync.js", "../../js/inject-header.js"], "asset_references": ["../../assets/images/gpace-logo-white.png", "../../assets/images/gpace-logo-white.png", "../../assets/images/gpace-logo-white.png"]}, "features\\workspace\\workspace.html": {"css_references": ["https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css", "https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css", "https://cdn.quilljs.com/1.3.6/quill.snow.css", "./workspace.css"], "js_references": ["https://cdnjs.cloudflare.com/ajax/libs/docx/7.8.2/docx.js", "https://cdn.quilljs.com/1.3.6/quill.min.js", "https://apis.google.com/js/api.js", "./speech-recognition.js", "./speech-synthesis.js", "./workspaceFlashcardIntegration.js", "./cross-tab-sync.js", "./workspace-ui.js", "./workspace-formatting.js", "./workspace-document.js", "./workspace-media.js", "./workspace-tables-links.js", "./workspace-attachments.js", "./workspace-core.js", "https://cdnjs.cloudflare.com/ajax/libs/html2pdf.js/0.10.1/html2pdf.bundle.min.js", "./sm2.js", "../../js/inject-header.js"], "asset_references": ["../../assets/images/gpace-logo-white.png", "../../assets/images/gpace-logo-white.png"]}}, "js_files": {"features\\academic-details\\academic-details.js": {"import_references": []}, "features\\flashcards\\flashcards.js": {"import_references": ["firebase/app", "firebase/firestore", "firebase/auth", "firebase/firestore", "firebase/firestore", "firebase/firestore", "firebase/firestore", "firebase/firestore", "firebase/firestore", "firebase/firestore", "firebase/firestore", "firebase/firestore", "firebase/firestore", "firebase/firestore", "firebase/firestore"]}, "features\\priority-calculator\\priority-calculator-with-worker.js": {"import_references": ["./js/priority-worker-wrapper"]}, "features\\priority-calculator\\priority-calculator.js": {"import_references": ["https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js"]}, "features\\subject-marks\\subject-marks.js": {"import_references": ["https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js", "https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js"]}}, "css_files": {}, "issues_found": ["features\\study-spaces\\study-spaces.html: Absolute path /socket.io/socket.io.js"]}